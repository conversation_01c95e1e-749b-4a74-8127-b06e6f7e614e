import 'package:flutter/material.dart';
import '../models/user.dart';
import '../services/user_service.dart';
import '../widgets/loading_widget.dart';

class UserFormScreen extends StatefulWidget {
  final User? user;

  const UserFormScreen({Key? key, this.user}) : super(key: key);

  @override
  State<UserFormScreen> createState() => _UserFormScreenState();
}

class _UserFormScreenState extends State<UserFormScreen> {
  final _formKey = GlobalKey<FormState>();
  final UserService _userService = UserService();

  // Form controllers
  final _usernameController = TextEditingController();
  final _emailController = TextEditingController();
  final _passwordController = TextEditingController();
  final _confirmPasswordController = TextEditingController();
  final _fullNameController = TextEditingController();
  final _departmentController = TextEditingController();

  String _selectedRole = 'Normal User';
  bool _isLoading = false;
  bool _obscurePassword = true;
  bool _obscureConfirmPassword = true;

  List<String> _roles = ['Admin', 'Normal User'];
  List<String> _departments = [];

  bool get _isEditing => widget.user != null;

  @override
  void initState() {
    super.initState();
    _loadInitialData();
    if (_isEditing) {
      _populateForm();
    }
  }

  @override
  void dispose() {
    _usernameController.dispose();
    _emailController.dispose();
    _passwordController.dispose();
    _confirmPasswordController.dispose();
    _fullNameController.dispose();
    _departmentController.dispose();
    super.dispose();
  }

  Future<void> _loadInitialData() async {
    try {
      final departments = await _userService.getDepartments();
      setState(() {
        _departments = departments;
      });
    } catch (e) {
      // Handle error silently
    }
  }

  void _populateForm() {
    final user = widget.user!;
    _usernameController.text = user.username;
    _emailController.text = user.email;
    _fullNameController.text = user.fullName ?? '';
    _departmentController.text = user.department ?? '';
    _selectedRole = user.role;
  }

  Future<void> _saveUser() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      if (_isEditing) {
        // Update existing user
        final request = UpdateUserRequest(
          username: _usernameController.text.trim(),
          email: _emailController.text.trim(),
          role: _selectedRole,
          fullName: _fullNameController.text.trim().isEmpty 
              ? null 
              : _fullNameController.text.trim(),
          department: _departmentController.text.trim().isEmpty 
              ? null 
              : _departmentController.text.trim(),
        );

        await _userService.updateUser(widget.user!.id, request);
        
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('用户更新成功')),
        );
      } else {
        // Create new user
        final request = CreateUserRequest(
          username: _usernameController.text.trim(),
          email: _emailController.text.trim(),
          password: _passwordController.text,
          role: _selectedRole,
          fullName: _fullNameController.text.trim().isEmpty 
              ? null 
              : _fullNameController.text.trim(),
          department: _departmentController.text.trim().isEmpty 
              ? null 
              : _departmentController.text.trim(),
        );

        await _userService.createUser(request);
        
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('用户创建成功')),
        );
      }

      Navigator.of(context).pop(true);
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('操作失败: $e')),
      );
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  String? _validateUsername(String? value) {
    if (value == null || value.trim().isEmpty) {
      return '请输入用户名';
    }
    if (value.trim().length < 3) {
      return '用户名至少需要3个字符';
    }
    if (value.trim().length > 50) {
      return '用户名不能超过50个字符';
    }
    return null;
  }

  String? _validateEmail(String? value) {
    if (value == null || value.trim().isEmpty) {
      return '请输入邮箱地址';
    }
    final emailRegex = RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$');
    if (!emailRegex.hasMatch(value.trim())) {
      return '请输入有效的邮箱地址';
    }
    return null;
  }

  String? _validatePassword(String? value) {
    if (_isEditing) return null; // Password not required for editing
    
    if (value == null || value.isEmpty) {
      return '请输入密码';
    }
    if (value.length < 6) {
      return '密码至少需要6个字符';
    }
    if (value.length > 100) {
      return '密码不能超过100个字符';
    }
    return null;
  }

  String? _validateConfirmPassword(String? value) {
    if (_isEditing) return null; // Password not required for editing
    
    if (value == null || value.isEmpty) {
      return '请确认密码';
    }
    if (value != _passwordController.text) {
      return '两次输入的密码不一致';
    }
    return null;
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(_isEditing ? '编辑用户' : '创建用户'),
        backgroundColor: Colors.blue[600],
        foregroundColor: Colors.white,
        actions: [
          if (_isLoading)
            const Padding(
              padding: EdgeInsets.all(16),
              child: SizedBox(
                width: 20,
                height: 20,
                child: CircularProgressIndicator(
                  strokeWidth: 2,
                  valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                ),
              ),
            ),
        ],
      ),
      body: _isLoading
          ? const LoadingWidget()
          : SingleChildScrollView(
              padding: const EdgeInsets.all(16),
              child: Form(
                key: _formKey,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.stretch,
                  children: [
                    _buildBasicInfoSection(),
                    const SizedBox(height: 24),
                    if (!_isEditing) _buildPasswordSection(),
                    if (!_isEditing) const SizedBox(height: 24),
                    _buildAdditionalInfoSection(),
                    const SizedBox(height: 32),
                    _buildActionButtons(),
                  ],
                ),
              ),
            ),
    );
  }

  Widget _buildBasicInfoSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              '基本信息',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            TextFormField(
              controller: _usernameController,
              decoration: const InputDecoration(
                labelText: '用户名 *',
                hintText: '请输入用户名',
                prefixIcon: Icon(Icons.person),
                border: OutlineInputBorder(),
              ),
              validator: _validateUsername,
              enabled: !_isLoading,
            ),
            const SizedBox(height: 16),
            TextFormField(
              controller: _emailController,
              decoration: const InputDecoration(
                labelText: '邮箱地址 *',
                hintText: '请输入邮箱地址',
                prefixIcon: Icon(Icons.email),
                border: OutlineInputBorder(),
              ),
              keyboardType: TextInputType.emailAddress,
              validator: _validateEmail,
              enabled: !_isLoading,
            ),
            const SizedBox(height: 16),
            DropdownButtonFormField<String>(
              value: _selectedRole,
              decoration: const InputDecoration(
                labelText: '角色 *',
                prefixIcon: Icon(Icons.security),
                border: OutlineInputBorder(),
              ),
              items: _roles.map((role) => DropdownMenuItem<String>(
                value: role,
                child: Text(role),
              )).toList(),
              onChanged: _isLoading ? null : (value) {
                setState(() {
                  _selectedRole = value!;
                });
              },
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return '请选择角色';
                }
                return null;
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPasswordSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              '密码设置',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            TextFormField(
              controller: _passwordController,
              decoration: InputDecoration(
                labelText: '密码 *',
                hintText: '请输入密码（至少6个字符）',
                prefixIcon: const Icon(Icons.lock),
                suffixIcon: IconButton(
                  icon: Icon(_obscurePassword ? Icons.visibility_off : Icons.visibility),
                  onPressed: () {
                    setState(() {
                      _obscurePassword = !_obscurePassword;
                    });
                  },
                ),
                border: const OutlineInputBorder(),
              ),
              obscureText: _obscurePassword,
              validator: _validatePassword,
              enabled: !_isLoading,
            ),
            const SizedBox(height: 16),
            TextFormField(
              controller: _confirmPasswordController,
              decoration: InputDecoration(
                labelText: '确认密码 *',
                hintText: '请再次输入密码',
                prefixIcon: const Icon(Icons.lock_outline),
                suffixIcon: IconButton(
                  icon: Icon(_obscureConfirmPassword ? Icons.visibility_off : Icons.visibility),
                  onPressed: () {
                    setState(() {
                      _obscureConfirmPassword = !_obscureConfirmPassword;
                    });
                  },
                ),
                border: const OutlineInputBorder(),
              ),
              obscureText: _obscureConfirmPassword,
              validator: _validateConfirmPassword,
              enabled: !_isLoading,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildAdditionalInfoSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              '附加信息',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            TextFormField(
              controller: _fullNameController,
              decoration: const InputDecoration(
                labelText: '姓名',
                hintText: '请输入真实姓名',
                prefixIcon: Icon(Icons.badge),
                border: OutlineInputBorder(),
              ),
              enabled: !_isLoading,
              validator: (value) {
                if (value != null && value.length > 100) {
                  return '姓名不能超过100个字符';
                }
                return null;
              },
            ),
            const SizedBox(height: 16),
            TextFormField(
              controller: _departmentController,
              decoration: InputDecoration(
                labelText: '部门',
                hintText: '请输入部门名称',
                prefixIcon: const Icon(Icons.business),
                border: const OutlineInputBorder(),
                suffixIcon: _departments.isNotEmpty
                    ? PopupMenuButton<String>(
                        icon: const Icon(Icons.arrow_drop_down),
                        onSelected: (value) {
                          _departmentController.text = value;
                        },
                        itemBuilder: (context) => _departments
                            .map((dept) => PopupMenuItem<String>(
                                  value: dept,
                                  child: Text(dept),
                                ))
                            .toList(),
                      )
                    : null,
              ),
              enabled: !_isLoading,
              validator: (value) {
                if (value != null && value.length > 100) {
                  return '部门名称不能超过100个字符';
                }
                return null;
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildActionButtons() {
    return Row(
      children: [
        Expanded(
          child: OutlinedButton(
            onPressed: _isLoading ? null : () => Navigator.of(context).pop(),
            child: const Text('取消'),
          ),
        ),
        const SizedBox(width: 16),
        Expanded(
          child: ElevatedButton(
            onPressed: _isLoading ? null : _saveUser,
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.blue[600],
              foregroundColor: Colors.white,
            ),
            child: Text(_isEditing ? '更新' : '创建'),
          ),
        ),
      ],
    );
  }
} 