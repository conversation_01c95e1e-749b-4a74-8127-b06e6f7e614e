import 'package:flutter/foundation.dart';
import '../models/user.dart';
import '../services/api_service.dart';

class AuthProvider with ChangeNotifier {
  final ApiService _apiService = ApiService();
  
  User? _currentUser;
  bool _isLoading = false;
  String? _errorMessage;

  User? get currentUser => _currentUser;
  bool get isLoading => _isLoading;
  String? get errorMessage => _errorMessage;
  bool get isLoggedIn => _currentUser != null && _apiService.isLoggedIn;

  // 初始化认证状态
  Future<void> initializeAuth() async {
    _setLoading(true);
    try {
      await _apiService.initializeToken();
      if (_apiService.isLoggedIn) {
        _currentUser = await _apiService.getCurrentUser();
      }
    } catch (e) {
      _setError('初始化认证失败: $e');
      await logout(); // 清除无效的token
    } finally {
      _setLoading(false);
    }
  }

  // 登录
  Future<bool> login(String username, String password) async {
    _setLoading(true);
    _clearError();
    
    try {
      final loginResponse = await _apiService.login(username, password);
      _currentUser = loginResponse.user;
      
      _setLoading(false);
      notifyListeners();
      return true;
    } catch (e) {
      _setError('登录失败: $e');
      _setLoading(false);
      return false;
    }
  }

  // 登出
  Future<void> logout() async {
    _setLoading(true);
    try {
      await _apiService.logout();
    } catch (e) {
      // 即使登出API调用失败，也要清除本地状态
      debugPrint('登出API调用失败: $e');
    } finally {
      _currentUser = null;
      _clearError();
      _setLoading(false);
      notifyListeners();
    }
  }

  // 刷新用户信息
  Future<void> refreshUser() async {
    if (!_apiService.isLoggedIn) return;
    
    try {
      _currentUser = await _apiService.getCurrentUser();
      notifyListeners();
    } catch (e) {
      _setError('刷新用户信息失败: $e');
    }
  }

  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  void _setError(String error) {
    _errorMessage = error;
    notifyListeners();
  }

  void _clearError() {
    _errorMessage = null;
    notifyListeners();
  }

  // 清除错误消息
  void clearError() {
    _clearError();
  }
}
