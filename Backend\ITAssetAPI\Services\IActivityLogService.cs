using ITAssetAPI.DTOs;
using ITAssetAPI.Models;

namespace ITAssetAPI.Services
{
    public interface IActivityLogService
    {
        Task<ActivityLogListResponseDto> GetActivityLogsAsync(int page, int limit, string? search = null, string? activityType = null, int? assetId = null, DateTime? startDate = null, DateTime? endDate = null);
        Task<ActivityLogDto> CreateActivityLogAsync(CreateActivityLogDto createActivityLogDto);
        Task LogAssetActivityAsync(string activityType, string description, int? assetId, int userId, string userName, string? assetName = null, string? assetNumber = null, object? oldValues = null, object? newValues = null);
        
        // 新增：组合活动日志方法
        Task LogCombinedAssetActivityAsync(
            ActivityType primaryType,
            List<ActivityType> secondaryTypes,
            int? assetId,
            int userId,
            string userName,
            string? assetName = null,
            string? assetNumber = null,
            object? oldValues = null,
            object? newValues = null);
    }
} 