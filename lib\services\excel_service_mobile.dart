import 'dart:io';
import 'package:path_provider/path_provider.dart';
import 'package:permission_handler/permission_handler.dart';

Future<String> saveFile(List<int> bytes, String fileName, {bool saveToDownload = false}) async {
  Directory? directory;
  
  if (saveToDownload && Platform.isAndroid) {
    // 请求存储权限
    var status = await Permission.storage.status;
    if (!status.isGranted) {
      status = await Permission.storage.request();
      if (!status.isGranted) {
        // 如果权限被拒绝，尝试请求管理外部存储权限（Android 11+）
        var manageStatus = await Permission.manageExternalStorage.status;
        if (!manageStatus.isGranted) {
          manageStatus = await Permission.manageExternalStorage.request();
          if (!manageStatus.isGranted) {
            throw Exception('需要存储权限才能保存到Download文件夹');
          }
        }
      }
    }
    
    try {
      // 尝试获取Download目录
      directory = Directory('/storage/emulated/0/Download');
      if (!await directory.exists()) {
        // 如果Download目录不存在，尝试其他路径
        final externalDir = await getExternalStorageDirectory();
        if (externalDir != null) {
          directory = Directory('${externalDir.path}/Download');
          if (!await directory.exists()) {
            await directory.create(recursive: true);
          }
        }
      }
    } catch (e) {
      // 如果无法访问Download目录，回退到应用目录
      directory = await getExternalStorageDirectory();
    }
  } else {
    // 保存到应用目录
    try {
      if (Platform.isAndroid) {
        directory = await getExternalStorageDirectory();
      } else {
        directory = await getApplicationDocumentsDirectory();
      }
    } catch (e) {
      directory = await getApplicationDocumentsDirectory();
    }
  }

  final filePath = '${directory!.path}/$fileName';
  final file = File(filePath);
  await file.writeAsBytes(bytes);
  return filePath;
} 