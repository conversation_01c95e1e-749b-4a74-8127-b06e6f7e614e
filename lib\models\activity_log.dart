import 'dart:convert';

enum ActivityType {
  create,
  update,
  delete,
  assign,
  unassign,
  maintenance,
  dispose,
}

class ActivityLog {
  final int id;
  final ActivityType activityType;
  final List<ActivityType> secondaryActivityTypes;
  final String description;
  final int? assetId;
  final int userId;
  final String userName;
  final String? assetName;
  final String? assetNumber;
  final String? oldValues;
  final String? newValues;
  final String? changeSummary;
  final DateTime createdAt;

  ActivityLog({
    required this.id,
    required this.activityType,
    this.secondaryActivityTypes = const [],
    required this.description,
    this.assetId,
    required this.userId,
    required this.userName,
    this.assetName,
    this.assetNumber,
    this.oldValues,
    this.newValues,
    this.changeSummary,
    required this.createdAt,
  });

  factory ActivityLog.fromJson(Map<String, dynamic> json) {
    return ActivityLog(
      id: json['id'],
      activityType: _parseActivityType(json['activityType']),
      secondaryActivityTypes: _parseSecondaryActivityTypes(json['secondaryActivityTypes']),
      description: json['description'] ?? '',
      assetId: json['assetId'],
      userId: json['userId'],
      userName: json['userName'] ?? '',
      assetName: json['assetName'],
      assetNumber: json['assetNumber'],
      oldValues: json['oldValues'],
      newValues: json['newValues'],
      changeSummary: json['changeSummary'],
      createdAt: DateTime.parse(json['createdAt']),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'activityType': _activityTypeToString(activityType),
      'secondaryActivityTypes': secondaryActivityTypes.map((t) => _activityTypeToString(t)).toList(),
      'description': description,
      'assetId': assetId,
      'userId': userId,
      'userName': userName,
      'assetName': assetName,
      'assetNumber': assetNumber,
      'oldValues': oldValues,
      'newValues': newValues,
      'changeSummary': changeSummary,
      'createdAt': createdAt.toIso8601String(),
    };
  }

  static ActivityType _parseActivityType(dynamic value) {
    if (value is int) {
      switch (value) {
        case 0:
          return ActivityType.create;
        case 1:
          return ActivityType.update;
        case 2:
          return ActivityType.delete;
        case 3:
          return ActivityType.assign;
        case 4:
          return ActivityType.unassign;
        case 5:
          return ActivityType.maintenance;
        case 6:
          return ActivityType.dispose;
        default:
          return ActivityType.create;
      }
    } else if (value is String) {
      switch (value.toLowerCase()) {
        case 'create':
          return ActivityType.create;
        case 'update':
          return ActivityType.update;
        case 'delete':
          return ActivityType.delete;
        case 'assign':
          return ActivityType.assign;
        case 'unassign':
          return ActivityType.unassign;
        case 'maintenance':
          return ActivityType.maintenance;
        case 'dispose':
          return ActivityType.dispose;
        default:
          return ActivityType.create;
      }
    }
    return ActivityType.create;
  }

  static List<ActivityType> _parseSecondaryActivityTypes(dynamic value) {
    if (value == null) return [];
    
    try {
      if (value is String) {
        final List<dynamic> types = jsonDecode(value);
        return types.map((t) => _parseActivityType(t)).toList();
      } else if (value is List) {
        return value.map((t) => _parseActivityType(t)).toList();
      }
    } catch (e) {
      print('Error parsing secondaryActivityTypes: $e');
    }
    
    return [];
  }

  static String _activityTypeToString(ActivityType type) {
    switch (type) {
      case ActivityType.create:
        return 'Create';
      case ActivityType.update:
        return 'Update';
      case ActivityType.delete:
        return 'Delete';
      case ActivityType.assign:
        return 'Assign';
      case ActivityType.unassign:
        return 'Unassign';
      case ActivityType.maintenance:
        return 'Maintenance';
      case ActivityType.dispose:
        return 'Dispose';
    }
  }

  String get activityTypeDisplayName {
    switch (activityType) {
      case ActivityType.create:
        return '创建';
      case ActivityType.update:
        return '更新';
      case ActivityType.delete:
        return '删除';
      case ActivityType.assign:
        return '分配';
      case ActivityType.unassign:
        return '取消分配';
      case ActivityType.maintenance:
        return '维护';
      case ActivityType.dispose:
        return '报废';
    }
  }

  static String getActivityTypeDisplayName(ActivityType type) {
    switch (type) {
      case ActivityType.create:
        return '创建';
      case ActivityType.update:
        return '更新';
      case ActivityType.delete:
        return '删除';
      case ActivityType.assign:
        return '分配';
      case ActivityType.unassign:
        return '取消分配';
      case ActivityType.maintenance:
        return '维护';
      case ActivityType.dispose:
        return '报废';
    }
  }
  
  String get allActivityTypesDisplayName {
    final allTypes = [activityType, ...secondaryActivityTypes];
    
    final uniqueTypes = <ActivityType>{};
    uniqueTypes.addAll(allTypes);
    
    return uniqueTypes.map((type) => getActivityTypeDisplayName(type)).join('、');
  }
}

class ActivityLogListResponse {
  final List<ActivityLog> activityLogs;
  final int totalCount;
  final int page;
  final int limit;
  final int totalPages;

  ActivityLogListResponse({
    required this.activityLogs,
    required this.totalCount,
    required this.page,
    required this.limit,
    required this.totalPages,
  });

  factory ActivityLogListResponse.fromJson(Map<String, dynamic> json) {
    return ActivityLogListResponse(
      activityLogs: (json['activityLogs'] as List)
          .map((item) => ActivityLog.fromJson(item))
          .toList(),
      totalCount: json['totalCount'],
      page: json['page'],
      limit: json['limit'],
      totalPages: json['totalPages'],
    );
  }
} 