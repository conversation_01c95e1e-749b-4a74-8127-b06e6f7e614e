using System;
using System.Collections.Concurrent;
using System.Threading;
using System.Threading.Tasks;

namespace ITAssetAPI.Services
{
    public interface IAsyncLock
    {
        Task<IDisposable> GetLockByKey(string key);
    }

    public class AsyncLock : IAsyncLock
    {
        private class Releaser : IDisposable
        {
            private readonly SemaphoreSlim _semaphore;

            public Releaser(SemaphoreSlim semaphore)
            {
                _semaphore = semaphore;
            }

            public void Dispose()
            {
                _semaphore.Release();
            }
        }

        private readonly ConcurrentDictionary<string, SemaphoreSlim> _locks = new ConcurrentDictionary<string, SemaphoreSlim>();

        public async Task<IDisposable> GetLockByKey(string key)
        {
            var semaphore = _locks.GetOrAdd(key, _ => new SemaphoreSlim(1, 1));
            await semaphore.WaitAsync();
            return new Releaser(semaphore);
        }
    }
} 