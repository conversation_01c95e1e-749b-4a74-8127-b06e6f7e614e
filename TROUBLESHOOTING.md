# IT资产管理应用故障排除指南

## 活动日志500错误解决方案（ActivityLogs表不存在）

**错误信息：** `42P01: relation "ActivityLogs" does not exist`

这是最常见的问题，表示数据库中缺少ActivityLogs表。

### 快速修复方法

运行以下脚本来自动修复所有问题：
```bash
fix_dependencies.bat
```

### 手动修复步骤

如果自动脚本失败，请按以下步骤手动修复：

1. **进入后端目录**：
   ```bash
   cd Backend\ITAssetAPI
   ```

2. **安装EF工具**：
   ```bash
   dotnet tool install --global dotnet-ef
   ```

3. **删除现有迁移**：
   ```bash
   rmdir /s /q Migrations
   ```

4. **创建新迁移**：
   ```bash
   dotnet ef migrations add InitialCreateWithActivityLogs
   ```

5. **重新创建数据库**：
   ```bash
   dotnet ef database drop --force
   dotnet ef database update
   ```

## 活动日志404错误解决方案

如果您遇到活动日志功能返回404错误，请按以下步骤操作：

### 1. 检查后端服务状态

首先确认后端服务是否正在运行：

```bash
# 检查端口5000是否被占用
netstat -an | findstr :5000
```

### 2. 启动后端服务

如果后端服务未运行，请按以下步骤启动：

#### 方法一：使用提供的脚本
```bash
# 首次运行需要设置数据库
setup_database.bat

# 启动后端服务
start_backend.bat
```

#### 方法二：手动启动
```bash
cd Backend\ITAssetAPI

# 还原包
dotnet restore

# 构建项目
dotnet build

# 运行服务
dotnet run --urls="http://localhost:5000"
```

### 3. 验证服务运行

服务启动后，您应该看到类似以下的输出：
```
info: Microsoft.Hosting.Lifetime[14]
      Now listening on: http://localhost:5000
info: Microsoft.Hosting.Lifetime[0]
      Application started. Press Ctrl+C to shut down.
```

### 4. 测试API连接

在Flutter应用中：
1. 进入"仪表板"
2. 点击右上角菜单
3. 选择"活动日志测试"
4. 点击"测试基础连接"按钮

### 5. 常见问题

#### 问题：端口5000被占用
**解决方案：**
- 修改 `Backend/ITAssetAPI/Properties/launchSettings.json` 中的端口
- 同时修改 `lib/services/api_service.dart` 中的 `baseUrl`

#### 问题：数据库连接失败
**解决方案：**
1. 确保PostgreSQL服务正在运行
2. 检查 `Backend/ITAssetAPI/appsettings.json` 中的连接字符串
3. 运行 `setup_database.bat` 重新创建数据库

#### 问题：认证失败
**解决方案：**
- 确保已登录应用
- 检查JWT token是否有效
- 重新登录应用

### 6. 调试步骤

1. **检查后端日志**：查看控制台输出的错误信息
2. **检查前端日志**：查看Flutter应用的调试输出
3. **使用测试屏幕**：使用"活动日志测试"功能进行诊断

### 7. 联系支持

如果问题仍然存在，请提供以下信息：
- 错误截图
- 后端控制台输出
- 前端调试日志
- 操作系统版本
- .NET版本信息

## 其他常见问题

### Excel导出功能
- 确保有足够的磁盘空间
- 检查文件权限
- 在Web版本中，文件会自动下载到浏览器默认下载文件夹

### 资产管理功能
- 确保数据库连接正常
- 检查用户权限
- 验证输入数据格式 