@echo off
echo 修复Flutter构建问题...
echo.

echo ========================================
echo 1. 清理Flutter缓存
echo ========================================
flutter clean

echo.
echo ========================================
echo 2. 获取依赖
echo ========================================
flutter pub get

echo.
echo ========================================
echo 3. 清理Android构建缓存
echo ========================================
cd android
call gradlew clean
cd ..

echo.
echo ========================================
echo 4. 重新构建
echo ========================================
flutter build apk --debug

echo.
echo ========================================
echo 修复完成！
echo ========================================
echo 现在可以运行: flutter run
echo.

pause 