using System.Text.Json;
using ITAssetAPI.Models;

namespace ITAssetAPI.DTOs
{
    public class ActivityLogDto
    {
        public int Id { get; set; }
        public ActivityType ActivityType { get; set; }
        public string? SecondaryActivityTypes { get; set; } // JSON格式存储次要活动类型
        public string Description { get; set; } = string.Empty;
        public int? AssetId { get; set; }
        public int UserId { get; set; }
        public string UserName { get; set; } = string.Empty;
        public string? AssetName { get; set; }
        public string? AssetNumber { get; set; }
        public string? OldValues { get; set; }
        public string? NewValues { get; set; }
        public string? ChangeSummary { get; set; }
        public DateTime CreatedAt { get; set; }

        // 从模型转换为DTO
        public static ActivityLogDto FromModel(ActivityLog model)
        {
            return new ActivityLogDto
            {
                Id = model.Id,
                ActivityType = model.ActivityType,
                SecondaryActivityTypes = model.SecondaryActivityTypes,
                Description = model.Description,
                AssetId = model.AssetId,
                UserId = model.UserId,
                UserName = model.UserName,
                AssetName = model.AssetName,
                AssetNumber = model.AssetNumber,
                OldValues = model.OldValues,
                NewValues = model.NewValues,
                ChangeSummary = model.ChangeSummary,
                CreatedAt = model.CreatedAt
            };
        }
    }

    public class CreateActivityLogDto
    {
        public ActivityType ActivityType { get; set; }
        public List<ActivityType> SecondaryActivityTypes { get; set; } = new();
        public string Description { get; set; } = string.Empty;
        public int? AssetId { get; set; }
        public int UserId { get; set; }
        public string UserName { get; set; } = string.Empty;
        public string? AssetName { get; set; }
        public string? AssetNumber { get; set; }
        public string? OldValues { get; set; }
        public string? NewValues { get; set; }
        public string? ChangeSummary { get; set; }
    }

    public class ActivityLogListResponseDto
    {
        public List<ActivityLogDto> ActivityLogs { get; set; } = new();
        public int TotalCount { get; set; }
        public int Page { get; set; }
        public int Limit { get; set; }
        public int TotalPages { get; set; }
    }
} 