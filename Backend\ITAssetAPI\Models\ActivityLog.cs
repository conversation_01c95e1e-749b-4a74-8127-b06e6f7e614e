using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Text.Json;

namespace ITAssetAPI.Models
{
    public enum ActivityType
    {
        Create,
        Update,
        Delete,
        Assign,
        Unassign,
        Maintenance,
        Dispose
    }

    public class ActivityLog
    {
        public int Id { get; set; }
        
        [Required]
        public ActivityType ActivityType { get; set; }
        
        public string? SecondaryActivityTypes { get; set; }
        
        [Required]
        [MaxLength(200)]
        public string Description { get; set; } = string.Empty;
        
        public int? AssetId { get; set; }
        
        [Required]
        public int UserId { get; set; }
        
        [Required]
        [MaxLength(100)]
        public string UserName { get; set; } = string.Empty;
        
        [MaxLength(100)]
        public string? AssetName { get; set; }
        
        [MaxLength(50)]
        public string? AssetNumber { get; set; }
        
        public string? OldValues { get; set; }
        
        public string? NewValues { get; set; }
        
        public string? ChangeSummary { get; set; }
        
        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
        
        // Navigation properties
        public Asset? Asset { get; set; }
        public User? User { get; set; }
        
        // Non-database field, used to store parsed secondary activity types
        [NotMapped]
        public List<ActivityType> SecondaryActivityTypesList 
        { 
            get 
            {
                if (string.IsNullOrEmpty(SecondaryActivityTypes))
                    return new List<ActivityType>();
                
                try
                {
                    return JsonSerializer.Deserialize<List<string>>(SecondaryActivityTypes)
                        .Select(t => Enum.Parse<ActivityType>(t))
                        .ToList();
                }
                catch
                {
                    return new List<ActivityType>();
                }
            }
        }
    }
} 