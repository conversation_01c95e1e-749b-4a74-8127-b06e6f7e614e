@echo off
echo 快速修复IT资产管理应用依赖问题...
echo.

echo ========================================
echo 1. 修复Flutter依赖
echo ========================================
echo 清理Flutter缓存...
flutter clean

echo 获取Flutter依赖...
flutter pub get

echo.
echo ========================================
echo 2. 修复后端数据库
echo ========================================
cd Backend\ITAssetAPI

echo 检查.NET环境...
dotnet --version
if %errorlevel% neq 0 (
    echo 错误: 未找到.NET SDK
    cd ..\..
    pause
    exit /b 1
)

echo 安装EF工具...
dotnet tool install --global dotnet-ef --skip-existing

echo 还原NuGet包...
dotnet restore

echo 构建项目...
dotnet build

echo 删除现有迁移...
if exist "Migrations" rmdir /s /q Migrations

echo 创建数据库迁移...
dotnet ef migrations add InitialCreateWithActivityLogs

echo 重新创建数据库...
dotnet ef database drop --force
dotnet ef database update

cd ..\..

echo.
echo ========================================
echo 修复完成！
echo ========================================
echo 现在可以：
echo 1. 运行 start_backend.bat 启动后端
echo 2. 运行 flutter run -d chrome --debug 启动前端
echo.

pause 