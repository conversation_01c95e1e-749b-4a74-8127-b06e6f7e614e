import 'package:flutter/foundation.dart';
import '../models/asset.dart';
import '../services/api_service.dart';

class AssetProvider with ChangeNotifier {
  final ApiService _apiService = ApiService();
  
  List<Asset> _assets = [];
  bool _isLoading = false;
  String? _errorMessage;
  
  // 搜索和过滤参数
  String _searchQuery = '';
  String? _selectedCategory;
  String? _selectedStatus;
  int _currentPage = 1;
  static const int _pageSize = 20;

  List<Asset> get assets => _assets;
  bool get isLoading => _isLoading;
  String? get errorMessage => _errorMessage;
  String get searchQuery => _searchQuery;
  String? get selectedCategory => _selectedCategory;
  String? get selectedStatus => _selectedStatus;
  int get currentPage => _currentPage;

  // 获取资产列表
  Future<void> fetchAssets({
    bool refresh = false,
    String? search,
    String? category,
    String? status,
  }) async {
    if (refresh) {
      _currentPage = 1;
      _assets.clear();
    }

    // 更新内部状态，如果传入null则清空对应的搜索条件
    _searchQuery = search ?? '';
    _selectedCategory = category;
    _selectedStatus = status;

    _setLoading(true);
    _clearError();

    try {
      final newAssets = await _apiService.getAssets(
        page: _currentPage,
        limit: _pageSize,
        search: _searchQuery.isNotEmpty ? _searchQuery : null,
        category: _selectedCategory,
        status: _selectedStatus,
      );

      if (refresh) {
        _assets = newAssets;
      } else {
        _assets.addAll(newAssets);
      }
      
      _currentPage++;
      _setLoading(false);
    } catch (e) {
      _setError('获取资产列表失败: $e');
      _setLoading(false);
    }
  }

  // 搜索资产
  Future<void> searchAssets(String query) async {
    _searchQuery = query;
    await fetchAssets(refresh: true);
  }

  // 按类别过滤
  Future<void> filterByCategory(String? category) async {
    _selectedCategory = category;
    await fetchAssets(refresh: true);
  }

  // 按状态过滤
  Future<void> filterByStatus(String? status) async {
    _selectedStatus = status;
    await fetchAssets(refresh: true);
  }

  // 清除所有过滤器
  Future<void> clearFilters() async {
    _searchQuery = '';
    _selectedCategory = null;
    _selectedStatus = null;
    await fetchAssets(refresh: true);
  }

  // 获取单个资产
  Future<Asset?> getAsset(String id) async {
    try {
      return await _apiService.getAsset(id);
    } catch (e) {
      _setError('获取资产详情失败: $e');
      return null;
    }
  }

  // 创建资产
  Future<bool> createAsset(Asset asset) async {
    _setLoading(true);
    _clearError();

    try {
      final newAsset = await _apiService.createAsset(asset);
      _assets.insert(0, newAsset); // 添加到列表开头
      _setLoading(false);
      return true;
    } catch (e) {
      _setError('创建资产失败: $e');
      _setLoading(false);
      return false;
    }
  }

  // 更新资产
  Future<bool> updateAsset(String id, Asset asset) async {
    _setLoading(true);
    _clearError();

    try {
      final updatedAsset = await _apiService.updateAsset(id, asset);
      final index = _assets.indexWhere((a) => a.id == id);
      if (index != -1) {
        _assets[index] = updatedAsset;
      }
      _setLoading(false);
      return true;
    } catch (e) {
      _setError('更新资产失败: $e');
      _setLoading(false);
      return false;
    }
  }

  // 删除资产
  Future<bool> deleteAsset(String id) async {
    _setLoading(true);
    _clearError();

    try {
      await _apiService.deleteAsset(id);
      _assets.removeWhere((asset) => asset.id == id);
      _setLoading(false);
      return true;
    } catch (e) {
      _setError('删除资产失败: $e');
      _setLoading(false);
      return false;
    }
  }

  // 刷新资产列表
  Future<void> refreshAssets() async {
    await fetchAssets(refresh: true);
  }

  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  void _setError(String error) {
    _errorMessage = error;
    notifyListeners();
  }

  void _clearError() {
    _errorMessage = null;
    notifyListeners();
  }

  // 获取下一个资产编号
  Future<String> getNextAssetNumber(AssetCategory category) async {
    try {
      return await _apiService.getNextAssetNumber(category);
    } catch (e) {
      _setError('获取资产编号失败: $e');
      return '';
    }
  }

  // 清除错误消息
  void clearError() {
    _clearError();
  }

  // 重置状态
  void reset() {
    _assets.clear();
    _searchQuery = '';
    _selectedCategory = null;
    _selectedStatus = null;
    _currentPage = 1;
    _clearError();
    notifyListeners();
  }
}
