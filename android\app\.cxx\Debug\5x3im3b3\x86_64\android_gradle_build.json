{"buildFiles": ["D:\\flutter\\packages\\flutter_tools\\gradle\\src\\main\\groovy\\CMakeLists.txt"], "cleanCommandsComponents": [["D:\\Android\\SDK\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "D:\\Android\\StudioProject\\ITAssetApp\\android\\app\\.cxx\\Debug\\5x3im3b3\\x86_64", "clean"]], "buildTargetsCommandComponents": ["D:\\Android\\SDK\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "D:\\Android\\StudioProject\\ITAssetApp\\android\\app\\.cxx\\Debug\\5x3im3b3\\x86_64", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {}, "toolchains": {"toolchain": {"cCompilerExecutable": "D:\\Android\\SDK\\ndk\\27.0.12077973\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang.exe", "cppCompilerExecutable": "D:\\Android\\SDK\\ndk\\27.0.12077973\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe"}}, "cFileExtensions": [], "cppFileExtensions": []}