import 'dart:convert';
import 'dart:math';
import '../models/user.dart';
import '../models/asset.dart';
import '../models/dashboard_stats.dart';

class MockApiService {
  // 模拟用户数据
  static final List<Map<String, dynamic>> _mockUsers = [
    {
      'id': '1',
      'username': 'admin',
      'password': 'admin123',
      'email': '<EMAIL>',
      'role': 'Admin',
      'fullName': '系统管理员',
      'department': 'IT部门',
      'createdAt': DateTime.now().subtract(const Duration(days: 30)).toIso8601String(),
      'updatedAt': DateTime.now().toIso8601String(),
    },
    {
      'id': '2',
      'username': 'user',
      'password': 'user123',
      'email': '<EMAIL>',
      'role': 'Normal User',
      'fullName': '普通用户',
      'department': '财务部门',
      'createdAt': DateTime.now().subtract(const Duration(days: 20)).toIso8601String(),
      'updatedAt': DateTime.now().toIso8601String(),
    },
  ];

  // 模拟资产数据
  static final List<Map<String, dynamic>> _mockAssets = [
    {
      'id': '1',
      'name': 'MacBook Pro 16"',
      'assetNumber': 'AST-001',
      'category': 'laptop',
      'status': 'assigned',
      'brand': 'Apple',
      'model': 'MacBook Pro 16" 2023',
      'serialNumber': 'MBP2023001',
      'assignedTo': '张三',
      'purchaseDate': '2023-01-15',
      'value': 18999.0,
      'vendor': '苹果授权经销商',
      'description': '高性能开发笔记本',
      'location': '研发部',
      'lastMaintenanceDate': '2023-06-15',
      'nextMaintenanceDate': '2024-06-15',
      'createdAt': DateTime.now().subtract(const Duration(days: 100)).toIso8601String(),
      'updatedAt': DateTime.now().subtract(const Duration(days: 10)).toIso8601String(),
    },
    {
      'id': '2',
      'name': 'Dell OptiPlex 7090',
      'assetNumber': 'AST-002',
      'category': 'desktop',
      'status': 'available',
      'brand': 'Dell',
      'model': 'OptiPlex 7090',
      'serialNumber': 'DELL2023002',
      'assignedTo': null,
      'purchaseDate': '2023-02-20',
      'value': 8999.0,
      'vendor': '戴尔中国',
      'description': '办公台式机',
      'location': '仓库',
      'lastMaintenanceDate': null,
      'nextMaintenanceDate': '2024-02-20',
      'createdAt': DateTime.now().subtract(const Duration(days: 90)).toIso8601String(),
      'updatedAt': DateTime.now().subtract(const Duration(days: 5)).toIso8601String(),
    },
    {
      'id': '3',
      'name': 'HP LaserJet Pro',
      'assetNumber': 'AST-003',
      'category': 'printer',
      'status': 'maintenance',
      'brand': 'HP',
      'model': 'LaserJet Pro M404dn',
      'serialNumber': 'HP2023003',
      'assignedTo': null,
      'purchaseDate': '2023-03-10',
      'value': 2999.0,
      'vendor': '惠普中国',
      'description': '激光打印机',
      'location': '办公区',
      'lastMaintenanceDate': '2023-12-01',
      'nextMaintenanceDate': '2024-03-01',
      'createdAt': DateTime.now().subtract(const Duration(days: 80)).toIso8601String(),
      'updatedAt': DateTime.now().subtract(const Duration(days: 2)).toIso8601String(),
    },
    {
      'id': '4',
      'name': 'iPhone 15 Pro',
      'assetNumber': 'AST-004',
      'category': 'phone',
      'status': 'assigned',
      'brand': 'Apple',
      'model': 'iPhone 15 Pro',
      'serialNumber': 'IP2023004',
      'assignedTo': '李四',
      'purchaseDate': '2023-09-20',
      'value': 9999.0,
      'vendor': '苹果授权经销商',
      'description': '公司手机',
      'location': '销售部',
      'lastMaintenanceDate': null,
      'nextMaintenanceDate': null,
      'createdAt': DateTime.now().subtract(const Duration(days: 60)).toIso8601String(),
      'updatedAt': DateTime.now().subtract(const Duration(days: 1)).toIso8601String(),
    },
    {
      'id': '5',
      'name': 'ThinkPad X1 Carbon',
      'assetNumber': 'AST-005',
      'category': 'laptop',
      'status': 'retired',
      'brand': 'Lenovo',
      'model': 'ThinkPad X1 Carbon Gen 8',
      'serialNumber': 'TP2023005',
      'assignedTo': null,
      'purchaseDate': '2020-05-15',
      'value': 12999.0,
      'vendor': '联想中国',
      'description': '已退役笔记本',
      'location': '仓库',
      'lastMaintenanceDate': '2023-05-15',
      'nextMaintenanceDate': null,
      'createdAt': DateTime.now().subtract(const Duration(days: 200)).toIso8601String(),
      'updatedAt': DateTime.now().subtract(const Duration(days: 30)).toIso8601String(),
    },
    {
      'id': '6',
      'name': 'LG UltraWide Monitor',
      'assetNumber': 'AST-006',
      'category': 'monitor',
      'status': 'available',
      'brand': 'LG',
      'model': '34WN750-B',
      'serialNumber': 'LG2023006',
      'assignedTo': null,
      'purchaseDate': '2023-04-12',
      'value': 3299.0,
      'vendor': 'LG电子',
      'description': '34寸超宽显示器',
      'location': '仓库',
      'lastMaintenanceDate': null,
      'nextMaintenanceDate': '2024-04-12',
      'createdAt': DateTime.now().subtract(const Duration(days: 70)).toIso8601String(),
      'updatedAt': DateTime.now().subtract(const Duration(days: 3)).toIso8601String(),
    },
    {
      'id': '7',
      'name': 'iPad Pro 12.9"',
      'assetNumber': 'AST-007',
      'category': 'tablet',
      'status': 'assigned',
      'brand': 'Apple',
      'model': 'iPad Pro 12.9" (6th generation)',
      'serialNumber': 'IPD2023007',
      'assignedTo': '王五',
      'purchaseDate': '2023-07-08',
      'value': 8799.0,
      'vendor': '苹果授权经销商',
      'description': '设计用平板电脑',
      'location': '设计部',
      'lastMaintenanceDate': null,
      'nextMaintenanceDate': null,
      'createdAt': DateTime.now().subtract(const Duration(days: 45)).toIso8601String(),
      'updatedAt': DateTime.now().toIso8601String(),
    },
  ];

  // 模拟登录
  static Future<LoginResponse> login(String username, String password) async {
    // 模拟网络延迟
    await Future.delayed(const Duration(milliseconds: 1000));

    final user = _mockUsers.firstWhere(
      (u) => u['username'] == username && u['password'] == password,
      orElse: () => throw Exception('用户名或密码错误'),
    );

    // 生成模拟token
    final token = _generateMockToken();
    
    return LoginResponse(
      token: token,
      user: User.fromJson(user),
    );
  }

  // 获取当前用户
  static Future<User> getCurrentUser(String token) async {
    await Future.delayed(const Duration(milliseconds: 500));
    
    // 从token中解析用户ID（这里简化处理）
    final userId = token.contains('admin') ? '1' : '2';
    final userData = _mockUsers.firstWhere((u) => u['id'] == userId);
    
    return User.fromJson(userData);
  }

  // 获取用户列表
  static Future<List<User>> getUsers() async {
    await Future.delayed(const Duration(milliseconds: 500));
    
    return _mockUsers.map((userData) => User.fromJson(userData)).toList();
  }

  // 获取资产列表
  static Future<List<Asset>> getAssets({
    int page = 1,
    int limit = 20,
    String? search,
    String? category,
    String? status,
  }) async {
    await Future.delayed(const Duration(milliseconds: 800));

    var filteredAssets = List<Map<String, dynamic>>.from(_mockAssets);

    // 搜索过滤
    if (search != null && search.isNotEmpty) {
      filteredAssets = filteredAssets.where((asset) {
        return asset['name'].toString().toLowerCase().contains(search.toLowerCase()) ||
               asset['serialNumber'].toString().toLowerCase().contains(search.toLowerCase());
      }).toList();
    }

    // 类别过滤
    if (category != null && category.isNotEmpty) {
      filteredAssets = filteredAssets.where((asset) => asset['category'] == category).toList();
    }

    // 状态过滤
    if (status != null && status.isNotEmpty) {
      filteredAssets = filteredAssets.where((asset) => asset['status'] == status).toList();
    }

    // 分页
    final startIndex = (page - 1) * limit;
    final endIndex = startIndex + limit;
    
    if (startIndex >= filteredAssets.length) {
      return [];
    }

    final paginatedAssets = filteredAssets.sublist(
      startIndex,
      endIndex > filteredAssets.length ? filteredAssets.length : endIndex,
    );

    return paginatedAssets.map((json) => Asset.fromJson(json)).toList();
  }

  // 获取单个资产
  static Future<Asset> getAsset(String id) async {
    await Future.delayed(const Duration(milliseconds: 500));
    
    final assetData = _mockAssets.firstWhere(
      (asset) => asset['id'] == id,
      orElse: () => throw Exception('资产未找到'),
    );
    
    return Asset.fromJson(assetData);
  }

  // 获取仪表板统计
  static Future<DashboardStats> getDashboardStats() async {
    await Future.delayed(const Duration(milliseconds: 1000));

    final totalAssets = _mockAssets.length;
    final assignedAssets = _mockAssets.where((a) => a['status'] == 'assigned').length;
    final availableAssets = _mockAssets.where((a) => a['status'] == 'available').length;
    final maintenanceAssets = _mockAssets.where((a) => a['status'] == 'maintenance').length;
    final retiredAssets = _mockAssets.where((a) => a['status'] == 'retired').length;

    // 按类别统计
    final categoryStats = <String, int>{};
    for (final asset in _mockAssets) {
      final category = asset['category'] as String;
      categoryStats[category] = (categoryStats[category] ?? 0) + 1;
    }

    return DashboardStats(
      totalAssets: totalAssets,
      assignedAssets: assignedAssets,
      availableAssets: availableAssets,
      maintenanceAssets: maintenanceAssets,
      retiredAssets: retiredAssets,
      categoryStats: categoryStats.entries
          .map((e) => CategoryStats(category: e.key, count: e.value))
          .toList(),
      monthlyStats: _generateMockMonthlyStats(),
    );
  }

  // 生成模拟token
  static String _generateMockToken() {
    final random = Random();
    final timestamp = DateTime.now().millisecondsSinceEpoch;
    final randomString = random.nextInt(999999).toString().padLeft(6, '0');
    return 'mock_token_${timestamp}_$randomString';
  }

  // 生成模拟月度统计
  static List<MonthlyStats> _generateMockMonthlyStats() {
    final months = ['1月', '2月', '3月', '4月', '5月', '6月'];
    final random = Random();
    
    return months.map((month) => MonthlyStats(
      month: month,
      purchased: random.nextInt(5) + 1,
      retired: random.nextInt(3),
    )).toList();
  }
}
