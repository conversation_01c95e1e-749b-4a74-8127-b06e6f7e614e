{"ConnectionStrings": {"DefaultConnection": "Host=localhost;Database=ITAssetDB;Username=postgres;Password=***"}, "Jwt": {"Key": "ITAssetApp_Super_Secret_Key_2024_For_Authentication_Token_Generation", "Issuer": "ITAssetAPI", "Audience": "ITAssetApp", "ExpireHours": 24}, "Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning", "Microsoft.EntityFrameworkCore.Database.Command": "Information"}}, "AllowedHosts": "*"}