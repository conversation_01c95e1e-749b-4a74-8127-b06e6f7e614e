// ignore: avoid_web_libraries_in_flutter
import 'dart:html' as html;

Future<String> saveFile(List<int> bytes, String fileName, {bool saveToDownload = false}) async {
  final blob = html.Blob([bytes]);
  final url = html.Url.createObjectUrlFromBlob(blob);
  final anchor = html.document.createElement('a') as html.AnchorElement
    ..href = url
    ..style.display = 'none'
    ..download = fileName;
  html.document.body?.children.add(anchor);
  anchor.click();
  html.document.body?.children.remove(anchor);
  html.Url.revokeObjectUrl(url);
  
  return '浏览器下载文件夹/$fileName';
} 