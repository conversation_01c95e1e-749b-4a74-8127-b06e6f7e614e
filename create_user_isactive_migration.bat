@echo off
echo Creating and applying User IsActive field migration...

cd Backend\ITAssetAPI

echo Step 1: Creating migration...
dotnet ef migrations add AddUserIsActiveField

echo Step 2: Updating database...
dotnet ef database update

echo Migration completed successfully!
echo.
echo The IsActive field has been added to the Users table.
echo All existing users are marked as active by default.
echo.
pause 