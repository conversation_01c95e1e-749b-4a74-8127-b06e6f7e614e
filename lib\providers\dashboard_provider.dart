import 'package:flutter/foundation.dart';
import '../models/dashboard_stats.dart';
import '../services/api_service.dart';

class DashboardProvider with ChangeNotifier {
  final ApiService _apiService = ApiService();
  
  DashboardStats? _stats;
  bool _isLoading = false;
  String? _errorMessage;

  DashboardStats? get stats => _stats;
  bool get isLoading => _isLoading;
  String? get errorMessage => _errorMessage;

  // 获取仪表板统计数据
  Future<void> fetchDashboardStats() async {
    _setLoading(true);
    _clearError();

    try {
      _stats = await _apiService.getDashboardStats();
      _setLoading(false);
    } catch (e) {
      _setError('获取仪表板数据失败: $e');
      _setLoading(false);
    }
  }

  // 刷新仪表板数据
  Future<void> refreshStats() async {
    await fetchDashboardStats();
  }

  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  void _setError(String error) {
    _errorMessage = error;
    notifyListeners();
  }

  void _clearError() {
    _errorMessage = null;
    notifyListeners();
  }

  // 清除错误消息
  void clearError() {
    _clearError();
  }

  // 重置状态
  void reset() {
    _stats = null;
    _clearError();
    notifyListeners();
  }
}
