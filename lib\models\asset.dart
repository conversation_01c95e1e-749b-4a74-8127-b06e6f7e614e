// 资产状态枚举
enum AssetStatus {
  available,
  assigned,
  maintenance,
  retired,
}

// 资产类别枚举
enum AssetCategory {
  laptop,
  desktop,
  monitor,
  printer,
  phone,
  tablet,
  server,
  network,
  other,
}

class Asset {
  final String? id;
  final String name;
  final String assetNumber;
  final AssetCategory category;
  final AssetStatus status;
  final String? brand;
  final String? model;
  final String? serialNumber;
  final String? assignedTo;
  final String? assignedUserId;
  final DateTime? purchaseDate;
  final double? value;
  final String? vendor;
  final String? description;
  final String? location;
  final DateTime? lastMaintenanceDate;
  final DateTime? nextMaintenanceDate;
  final DateTime? createdAt;
  final DateTime? updatedAt;

  Asset({
    this.id,
    required this.name,
    required this.assetNumber,
    required this.category,
    required this.status,
    this.brand,
    this.model,
    this.serialNumber,
    this.assignedTo,
    this.assignedUserId,
    this.purchaseDate,
    this.value,
    this.vendor,
    this.description,
    this.location,
    this.lastMaintenanceDate,
    this.nextMaintenanceDate,
    this.createdAt,
    this.updatedAt,
  });

  factory Asset.fromJson(Map<String, dynamic> json) {
    return Asset(
      id: json['id']?.toString(),
      name: json['name'],
      assetNumber: json['assetNumber'] ?? json['serialNumber'] ?? '',
      category: _parseCategoryFromBackend(json['category']),
      status: _parseStatusFromBackend(json['status']),
      brand: json['brand'],
      model: json['model'],
      serialNumber: json['serialNumber'],
      assignedTo: json['assignedTo'],
      assignedUserId: json['assignedUserId']?.toString(),
      purchaseDate: json['purchaseDate'] != null 
          ? DateTime.parse(json['purchaseDate']) 
          : null,
      value: json['value']?.toDouble() ?? json['purchasePrice']?.toDouble(),
      vendor: json['vendor'],
      description: json['description'],
      location: json['location'],
      lastMaintenanceDate: json['lastMaintenanceDate'] != null 
          ? DateTime.parse(json['lastMaintenanceDate']) 
          : null,
      nextMaintenanceDate: json['nextMaintenanceDate'] != null 
          ? DateTime.parse(json['nextMaintenanceDate']) 
          : null,
      createdAt: json['createdAt'] != null 
          ? DateTime.parse(json['createdAt']) 
          : null,
      updatedAt: json['updatedAt'] != null 
          ? DateTime.parse(json['updatedAt']) 
          : null,
    );
  }

  static AssetCategory _parseCategoryFromBackend(dynamic category) {
    if (category is int) {
      switch (category) {
        case 0: return AssetCategory.laptop;
        case 1: return AssetCategory.desktop;
        case 2: return AssetCategory.monitor;
        case 3: return AssetCategory.printer;
        case 4: return AssetCategory.phone;
        case 5: return AssetCategory.tablet;
        case 6: return AssetCategory.server;
        case 7: return AssetCategory.network;
        case 8: return AssetCategory.other;
        default: return AssetCategory.other;
      }
    } else if (category is String) {
      return _parseCategory(category);
    }
    return AssetCategory.other;
  }

  static AssetStatus _parseStatusFromBackend(dynamic status) {
    if (status is int) {
      switch (status) {
        case 0: return AssetStatus.available;
        case 1: return AssetStatus.assigned;
        case 2: return AssetStatus.maintenance;
        case 3: return AssetStatus.retired;
        default: return AssetStatus.available;
      }
    } else if (status is String) {
      return _parseStatus(status);
    }
    return AssetStatus.available;
  }

  static AssetCategory _parseCategory(String? category) {
    switch (category?.toLowerCase()) {
      case 'laptop':
        return AssetCategory.laptop;
      case 'desktop':
        return AssetCategory.desktop;
      case 'monitor':
        return AssetCategory.monitor;
      case 'printer':
        return AssetCategory.printer;
      case 'phone':
        return AssetCategory.phone;
      case 'tablet':
        return AssetCategory.tablet;
      case 'server':
        return AssetCategory.server;
      case 'network':
      case 'network equipment':
        return AssetCategory.network;
      default:
        return AssetCategory.other;
    }
  }

  static AssetStatus _parseStatus(String? status) {
    switch (status?.toLowerCase()) {
      case 'available':
        return AssetStatus.available;
      case 'assigned':
        return AssetStatus.assigned;
      case 'maintenance':
        return AssetStatus.maintenance;
      case 'retired':
        return AssetStatus.retired;
      default:
        return AssetStatus.available;
    }
  }

  Map<String, dynamic> toJson() {
    final json = <String, dynamic>{
      'name': name,
      'assetNumber': assetNumber,
      'category': _categoryToBackendFormat(category),
      'status': _statusToBackendFormat(status),
    };
    
    // 只添加非空的可选字段
    if (brand != null && brand!.isNotEmpty) json['brand'] = brand;
    if (model != null && model!.isNotEmpty) json['model'] = model;
    if (serialNumber != null && serialNumber!.isNotEmpty) json['serialNumber'] = serialNumber;
    // 处理分配信息，始终发送分配信息（如果有的话）
    if (assignedUserId != null && assignedUserId!.isNotEmpty) {
      json['assignedUserId'] = int.tryParse(assignedUserId!) ?? 0;
      if (assignedTo != null && assignedTo!.isNotEmpty) {
        json['assignedTo'] = assignedTo;
      }
    } else {
      // 未分配状态 - 只在更新时发送0，创建时不发送这些字段
      if (id != null) {
        // 这是更新操作，需要明确告诉后端清空分配
        json['assignedUserId'] = 0; // 0表示未分配，后端会将其转换为null
        json['assignedTo'] = ""; // 发送空字符串，确保后端清空该字段
      }
      // 创建时如果未分配，不发送这些字段，让后端使用默认值
    }
    if (purchaseDate != null) json['purchaseDate'] = purchaseDate!.toIso8601String();
    if (value != null) json['value'] = value;
    if (vendor != null && vendor!.isNotEmpty) json['vendor'] = vendor;
    if (description != null && description!.isNotEmpty) json['description'] = description;
    if (location != null && location!.isNotEmpty) json['location'] = location;
    if (lastMaintenanceDate != null) json['lastMaintenanceDate'] = lastMaintenanceDate!.toIso8601String();
    if (nextMaintenanceDate != null) json['nextMaintenanceDate'] = nextMaintenanceDate!.toIso8601String();
    
    return json;
  }

  // 将前端枚举转换为后端期待的格式
  static String _categoryToBackendFormat(AssetCategory category) {
    switch (category) {
      case AssetCategory.laptop:
        return 'Laptop';
      case AssetCategory.desktop:
        return 'Desktop';
      case AssetCategory.monitor:
        return 'Monitor';
      case AssetCategory.printer:
        return 'Printer';
      case AssetCategory.phone:
        return 'Phone';
      case AssetCategory.tablet:
        return 'Tablet';
      case AssetCategory.server:
        return 'Server';
      case AssetCategory.network:
        return 'Network';
      case AssetCategory.other:
        return 'Other';
    }
  }

  static String _statusToBackendFormat(AssetStatus status) {
    switch (status) {
      case AssetStatus.available:
        return 'Available';
      case AssetStatus.assigned:
        return 'Assigned';
      case AssetStatus.maintenance:
        return 'Maintenance';
      case AssetStatus.retired:
        return 'Retired';
    }
  }

  // 将前端枚举转换为后端期待的整数索引
  static int _categoryToBackendIndex(AssetCategory category) {
    switch (category) {
      case AssetCategory.laptop:
        return 0;
      case AssetCategory.desktop:
        return 1;
      case AssetCategory.monitor:
        return 2;
      case AssetCategory.printer:
        return 3;
      case AssetCategory.phone:
        return 4;
      case AssetCategory.tablet:
        return 5;
      case AssetCategory.server:
        return 6;
      case AssetCategory.network:
        return 7;
      case AssetCategory.other:
        return 8;
    }
  }

  static int _statusToBackendIndex(AssetStatus status) {
    switch (status) {
      case AssetStatus.available:
        return 0;
      case AssetStatus.assigned:
        return 1;
      case AssetStatus.maintenance:
        return 2;
      case AssetStatus.retired:
        return 3;
    }
  }

  Asset copyWith({
    String? id,
    String? name,
    String? assetNumber,
    AssetCategory? category,
    AssetStatus? status,
    String? brand,
    String? model,
    String? serialNumber,
    String? assignedTo,
    String? assignedUserId,
    DateTime? purchaseDate,
    double? value,
    String? vendor,
    String? description,
    String? location,
    DateTime? lastMaintenanceDate,
    DateTime? nextMaintenanceDate,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return Asset(
      id: id ?? this.id,
      name: name ?? this.name,
      assetNumber: assetNumber ?? this.assetNumber,
      category: category ?? this.category,
      status: status ?? this.status,
      brand: brand ?? this.brand,
      model: model ?? this.model,
      serialNumber: serialNumber ?? this.serialNumber,
      assignedTo: assignedTo ?? this.assignedTo,
      assignedUserId: assignedUserId ?? this.assignedUserId,
      purchaseDate: purchaseDate ?? this.purchaseDate,
      value: value ?? this.value,
      vendor: vendor ?? this.vendor,
      description: description ?? this.description,
      location: location ?? this.location,
      lastMaintenanceDate: lastMaintenanceDate ?? this.lastMaintenanceDate,
      nextMaintenanceDate: nextMaintenanceDate ?? this.nextMaintenanceDate,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }
}
