import 'package:flutter/material.dart';

class ScrollToTopButton extends StatefulWidget {
  final ScrollController scrollController;
  final double showOffset;
  final Duration animationDuration;
  final Color? backgroundColor;
  final Color? iconColor;
  final double? size;

  const ScrollToTopButton({
    super.key,
    required this.scrollController,
    this.showOffset = 200.0,
    this.animationDuration = const Duration(milliseconds: 300),
    this.backgroundColor,
    this.iconColor,
    this.size = 56.0,
  });

  @override
  State<ScrollToTopButton> createState() => _ScrollToTopButtonState();
}

class _ScrollToTopButtonState extends State<ScrollToTopButton>
    with SingleTickerProviderStateMixin {
  bool _isVisible = false;
  late AnimationController _animationController;
  late Animation<double> _scaleAnimation;
  late Animation<double> _opacityAnimation;

  @override
  void initState() {
    super.initState();
    
    _animationController = AnimationController(
      duration: widget.animationDuration,
      vsync: this,
    );
    
    _scaleAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.elasticOut,
    ));
    
    _opacityAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));

    widget.scrollController.addListener(_scrollListener);
  }

  @override
  void dispose() {
    widget.scrollController.removeListener(_scrollListener);
    _animationController.dispose();
    super.dispose();
  }

  void _scrollListener() {
    final shouldShow = widget.scrollController.offset > widget.showOffset;
    
    if (shouldShow != _isVisible) {
      setState(() {
        _isVisible = shouldShow;
      });
      
      if (_isVisible) {
        _animationController.forward();
      } else {
        _animationController.reverse();
      }
    }
  }

  void _scrollToTop() {
    widget.scrollController.animateTo(
      0.0,
      duration: const Duration(milliseconds: 500),
      curve: Curves.easeInOut,
    );
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _animationController,
      builder: (context, child) {
        return Transform.scale(
          scale: _scaleAnimation.value,
          child: Opacity(
            opacity: _opacityAnimation.value,
            child: FloatingActionButton(
              onPressed: _scrollToTop,
              backgroundColor: widget.backgroundColor ?? Theme.of(context).primaryColor,
              foregroundColor: widget.iconColor ?? Colors.white,
              elevation: 8.0,
              mini: widget.size! < 56.0,
              child: const Icon(
                Icons.keyboard_arrow_up,
                size: 28,
              ),
            ),
          ),
        );
      },
    );
  }
}

class ScrollToTopWrapper extends StatefulWidget {
  final Widget child;
  final double showOffset;
  final Duration animationDuration;
  final Color? backgroundColor;
  final Color? iconColor;
  final double? buttonSize;
  final EdgeInsets? buttonMargin;

  const ScrollToTopWrapper({
    super.key,
    required this.child,
    this.showOffset = 200.0,
    this.animationDuration = const Duration(milliseconds: 300),
    this.backgroundColor,
    this.iconColor,
    this.buttonSize = 56.0,
    this.buttonMargin = const EdgeInsets.only(right: 16, bottom: 16),
  });

  @override
  State<ScrollToTopWrapper> createState() => _ScrollToTopWrapperState();
}

class _ScrollToTopWrapperState extends State<ScrollToTopWrapper> {
  late ScrollController _scrollController;

  @override
  void initState() {
    super.initState();
    _scrollController = ScrollController();
  }

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        // 主要内容，使用我们的ScrollController
        _wrapWithScrollController(widget.child),
        
        // 回到顶部按钮
        Positioned(
          right: widget.buttonMargin?.right ?? 16,
          bottom: widget.buttonMargin?.bottom ?? 16,
          child: ScrollToTopButton(
            scrollController: _scrollController,
            showOffset: widget.showOffset,
            animationDuration: widget.animationDuration,
            backgroundColor: widget.backgroundColor,
            iconColor: widget.iconColor,
            size: widget.buttonSize,
          ),
        ),
      ],
    );
  }

  Widget _wrapWithScrollController(Widget child) {
    // 如果child是ListView、GridView等可滚动组件，我们需要替换它们的controller
    if (child is ListView) {
      return ListView.builder(
        controller: _scrollController,
        padding: child.padding,
        itemCount: (child as ListView).semanticChildCount,
        itemBuilder: (context, index) {
          // 这里需要根据实际情况调整
          return Container(); // 占位符
        },
      );
    }
    
    // 对于其他情况，包装在SingleChildScrollView中
    return SingleChildScrollView(
      controller: _scrollController,
      child: child,
    );
  }
} 