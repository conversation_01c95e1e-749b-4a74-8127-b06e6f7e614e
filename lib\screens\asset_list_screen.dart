import 'dart:async';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:go_router/go_router.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import '../providers/asset_provider.dart';
import '../models/asset.dart';
import '../config/routes.dart';
import '../services/excel_service.dart';
import '../widgets/main_layout.dart';
import '../widgets/animated_search_bar.dart';

class AssetListScreen extends StatefulWidget {
  const AssetListScreen({super.key});

  @override
  State<AssetListScreen> createState() => _AssetListScreenState();
}

class _AssetListScreenState extends State<AssetListScreen> with WidgetsBindingObserver {
  final TextEditingController _searchController = TextEditingController();
  final ScrollController _scrollController = ScrollController();
  Timer? _debounceTimer;
  
  List<String> _selectedCategories = [];
  List<String> _selectedStatuses = [];
  bool _showScrollToTop = false;
  bool _isSearching = false;
  
  // 批量操作相关状态
  bool _isBatchMode = false;
  Set<String> _selectedAssetIds = <String>{};

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addObserver(this);
    _scrollController.addListener(_onScroll);
    _searchController.addListener(_onSearchChanged);
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _loadAssets();
    });
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    super.didChangeAppLifecycleState(state);
    // 当应用从后台回到前台时刷新数据
    if (state == AppLifecycleState.resumed) {
      _loadAssets();
    }
  }

  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
    _searchController.dispose();
    _scrollController.dispose();
    _debounceTimer?.cancel();
    super.dispose();
  }

  Future<void> _loadAssets() async {
    final assetProvider = Provider.of<AssetProvider>(context, listen: false);
    await assetProvider.fetchAssets(
      refresh: true,
      search: _searchController.text.isEmpty ? null : _searchController.text,
      category: _selectedCategories.isNotEmpty ? _selectedCategories.first : null,
      status: _selectedStatuses.isNotEmpty ? _selectedStatuses.first : null,
    );
  }

  Future<void> _handleRefresh() async {
    await _loadAssets();
  }

  void _onSearchChanged() {
    if (_debounceTimer?.isActive ?? false) _debounceTimer!.cancel();
    _debounceTimer = Timer(const Duration(milliseconds: 500), () {
      if (mounted) {
        _loadAssets();
      }
    });
    
    // 更新搜索状态
    setState(() {
      _isSearching = _searchController.text.isNotEmpty;
    });
  }

  void _onFilterChanged() {
    _loadAssets();
  }

  void _onScroll() {
    if (_scrollController.offset >= 400 && !_showScrollToTop) {
      setState(() {
        _showScrollToTop = true;
      });
    } else if (_scrollController.offset < 400 && _showScrollToTop) {
      setState(() {
        _showScrollToTop = false;
      });
    }
  }

  void _scrollToTop() {
    _scrollController.animateTo(
      0,
      duration: const Duration(milliseconds: 500),
      curve: Curves.easeInOut,
    );
  }

  void _clearFilters() {
    setState(() {
      _searchController.clear();
      _selectedCategories.clear();
      _selectedStatuses.clear();
    });
    _loadAssets();
  }

  // 批量操作相关方法
  void _enterBatchMode() {
    setState(() {
      _isBatchMode = true;
      _selectedAssetIds.clear();
    });
  }

  void _exitBatchMode() {
    setState(() {
      _isBatchMode = false;
      _selectedAssetIds.clear();
    });
  }

  void _toggleAssetSelection(String assetId) {
    setState(() {
      if (_selectedAssetIds.contains(assetId)) {
        _selectedAssetIds.remove(assetId);
      } else {
        _selectedAssetIds.add(assetId);
      }
    });
  }

  void _selectAllAssets() {
    final assetProvider = Provider.of<AssetProvider>(context, listen: false);
    setState(() {
      if (_selectedAssetIds.length == assetProvider.assets.length) {
        _selectedAssetIds.clear(); // 如果已全选，则取消全选
      } else {
        _selectedAssetIds = assetProvider.assets.map<String>((asset) => asset.id!).toSet();
      }
    });
  }

  Future<void> _batchDeleteAssets() async {
    if (_selectedAssetIds.isEmpty) return;

    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('确认批量删除'),
        content: Text('确定要删除选中的 ${_selectedAssetIds.length} 个资产吗？\n此操作无法撤销。'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('取消'),
          ),
          ElevatedButton(
            onPressed: () => Navigator.of(context).pop(true),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.red,
              foregroundColor: Colors.white,
            ),
            child: const Text('删除'),
          ),
        ],
      ),
    );

    if (confirmed == true && mounted) {
      final assetProvider = Provider.of<AssetProvider>(context, listen: false);
      
      // 显示加载对话框
      showDialog(
        context: context,
        barrierDismissible: false,
        builder: (context) => AlertDialog(
          content: Row(
            children: [
              const CircularProgressIndicator(),
              const SizedBox(width: 16),
              Text('正在删除 ${_selectedAssetIds.length} 个资产...'),
            ],
          ),
        ),
      );

      int successCount = 0;
      int failCount = 0;

      // 逐个删除资产
      for (final assetId in _selectedAssetIds) {
        final success = await assetProvider.deleteAsset(assetId);
        if (success) {
          successCount++;
        } else {
          failCount++;
        }
      }

      // 关闭加载对话框
      if (mounted) {
        Navigator.of(context).pop();
        
        // 退出批量模式
        _exitBatchMode();
        
        // 显示结果
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              failCount == 0 
                ? '成功删除 $successCount 个资产'
                : '删除完成：成功 $successCount 个，失败 $failCount 个'
            ),
            backgroundColor: failCount == 0 ? Colors.green : Colors.orange,
          ),
        );
      }
    }
  }

  void _showFilterBottomSheet() {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => StatefulBuilder(
        builder: (context, setModalState) => Container(
          decoration: const BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.vertical(top: Radius.circular(24)),
          ),
          padding: const EdgeInsets.fromLTRB(24, 20, 24, 32),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
            // 顶部指示器
            Center(
              child: Container(
                width: 40,
                height: 4,
                decoration: BoxDecoration(
                  color: Colors.grey[300],
                  borderRadius: BorderRadius.circular(2),
                ),
              ),
            ),
            const SizedBox(height: 20),
            // 标题
            Row(
              children: [
                Icon(Icons.tune_rounded, color: Colors.grey[700], size: 24),
                const SizedBox(width: 12),
                Text(
                  '筛选条件',
                  style: TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.w700,
                    color: Colors.grey[900],
                  ),
                ),
                const Spacer(),
                                 if (_selectedCategories.isNotEmpty || _selectedStatuses.isNotEmpty)
                   TextButton(
                     onPressed: () {
                       setState(() {
                         _selectedCategories.clear();
                         _selectedStatuses.clear();
                       });
                       setModalState(() {}); // 立即更新弹窗UI
                       _onFilterChanged();
                       Navigator.pop(context);
                     },
                    child: Text(
                      '清除',
                      style: TextStyle(
                        color: Colors.blue[600],
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ),
              ],
            ),
            const SizedBox(height: 24),
            // 分类筛选
            Text(
              '资产分类',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w600,
                color: Colors.grey[800],
              ),
            ),
            const SizedBox(height: 12),
                         Wrap(
               spacing: 8,
               runSpacing: 8,
               children: [
                 _buildMultiSelectChip('全部分类', null, _selectedCategories, (value) {
                   setState(() {
                     if (value == null) {
                       _selectedCategories.clear();
                     }
                   });
                   setModalState(() {}); // 立即更新弹窗UI
                 }),
                 ...AssetCategory.values.map((category) => 
                   _buildMultiSelectChip(
                     _getCategoryDisplayName(category), 
                     category.name, 
                     _selectedCategories, 
                     (value) {
                       setState(() {
                         if (value != null) {
                           if (_selectedCategories.contains(value)) {
                             _selectedCategories.remove(value);
                           } else {
                             _selectedCategories.add(value);
                           }
                         }
                       });
                       setModalState(() {}); // 立即更新弹窗UI
                     }
                   )
                 ),
               ],
             ),
            const SizedBox(height: 24),
            // 状态筛选
            Text(
              '资产状态',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w600,
                color: Colors.grey[800],
              ),
            ),
            const SizedBox(height: 12),
                         Wrap(
               spacing: 8,
               runSpacing: 8,
               children: [
                 _buildMultiSelectChip('全部状态', null, _selectedStatuses, (value) {
                   setState(() {
                     if (value == null) {
                       _selectedStatuses.clear();
                     }
                   });
                   setModalState(() {}); // 立即更新弹窗UI
                 }),
                 ...AssetStatus.values.map((status) => 
                   _buildMultiSelectChip(
                     _getStatusDisplayName(status), 
                     status.name, 
                     _selectedStatuses, 
                     (value) {
                       setState(() {
                         if (value != null) {
                           if (_selectedStatuses.contains(value)) {
                             _selectedStatuses.remove(value);
                           } else {
                             _selectedStatuses.add(value);
                           }
                         }
                       });
                       setModalState(() {}); // 立即更新弹窗UI
                     }
                   )
                 ),
               ],
             ),
            const SizedBox(height: 32),
            // 应用按钮
            SizedBox(
              width: double.infinity,
              child: Container(
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    colors: [Colors.blue[600]!, Colors.blue[700]!],
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                  ),
                  borderRadius: BorderRadius.circular(16),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.blue.withOpacity(0.3),
                      spreadRadius: 0,
                      blurRadius: 8,
                      offset: const Offset(0, 4),
                    ),
                  ],
                ),
                child: ElevatedButton(
                  onPressed: () {
                    _onFilterChanged();
                    Navigator.pop(context);
                  },
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.transparent,
                    shadowColor: Colors.transparent,
                    padding: const EdgeInsets.symmetric(vertical: 16),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(16),
                    ),
                  ),
                  child: const Text(
                    '应用筛选',
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    ),
    );
  }

  Widget _buildMultiSelectChip(String label, String? value, List<String> selectedValues, Function(String?) onTap) {
    final bool isSelected = value == null ? selectedValues.isEmpty : selectedValues.contains(value);
    final bool isAllOption = value == null;
    
    return GestureDetector(
      onTap: () => onTap(value),
      child: Container(
        height: 40, // 固定高度
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 10),
        decoration: BoxDecoration(
          color: isSelected ? Colors.blue[600] : Colors.grey[100],
          borderRadius: BorderRadius.circular(12),
          border: Border.all(
            color: isSelected ? Colors.blue[600]! : Colors.grey[200]!,
            width: 1,
          ),
        ),
        child: Stack(
          alignment: Alignment.center,
          children: [
            // 文字始终居中
            Text(
              label,
              style: TextStyle(
                color: isSelected ? Colors.white : Colors.grey[700],
                fontSize: 14,
                fontWeight: isSelected ? FontWeight.w600 : FontWeight.w500,
              ),
            ),
            // 勾选图标叠加在左侧
            if (!isAllOption && isSelected)
              Positioned(
                left: 0,
                child: Icon(
                  Icons.check_rounded,
                  color: Colors.white,
                  size: 16,
                ),
              ),
          ],
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: MainLayout(
        currentRoute: AppRoutes.assetList,
        child: Stack(
          children: [
            Column(
              children: [
                // 现代化简洁工具栏
                Container(
                  padding: const EdgeInsets.fromLTRB(20, 12, 20, 16),
                  decoration: BoxDecoration(
                    color: Colors.white,
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black.withOpacity(0.03),
                        spreadRadius: 0,
                        blurRadius: 12,
                        offset: const Offset(0, 2),
                      ),
                    ],
                  ),
                  child: Column(
                    children: [
                      // 根据模式显示不同的工具栏
                      _isBatchMode ? _buildBatchModeToolbar() : _buildNormalModeToolbar(),
                    ],
                  ),
                ),
                // 资产列表
                Expanded(
                  child: _buildAssetList(),
                ),
              ],
            ),
            // 返回顶部按钮 - 正确位置
            if (_showScrollToTop)
              Positioned(
                right: 20,
                bottom: 100, // 避开底部导航栏
                child: Container(
                  decoration: BoxDecoration(
                    color: Colors.blue[600],
                    borderRadius: BorderRadius.circular(16),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.blue.withOpacity(0.3),
                        spreadRadius: 0,
                        blurRadius: 12,
                        offset: const Offset(0, 4),
                      ),
                    ],
                  ),
                  child: Material(
                    color: Colors.transparent,
                    child: InkWell(
                      onTap: _scrollToTop,
                      borderRadius: BorderRadius.circular(16),
                      child: Container(
                        width: 48,
                        height: 48,
                        child: const Icon(
                          Icons.keyboard_arrow_up_rounded,
                          color: Colors.white,
                          size: 24,
                        ),
                      ),
                    ),
                  ),
                ),
              ),
          ],
        ),
      ),

    );
  }

  // 普通模式工具栏
  Widget _buildNormalModeToolbar() {
    return Row(
      children: [
        // 动画搜索框
        Expanded(
          child: AnimatedSearchBar(
            controller: _searchController,
            hintText: '搜索资产名称、编号或描述...',
            isSearching: _isSearching,
            onChanged: (value) {
              setState(() {}); // 更新UI
            },
            onClear: () {
              setState(() {}); // 更新UI
            },
          ),
        ),
        const SizedBox(width: 12),
        // 导出按钮
        Container(
          decoration: BoxDecoration(
            color: Colors.grey[50],
            borderRadius: BorderRadius.circular(12),
            border: Border.all(color: Colors.grey[200]!, width: 1),
          ),
          child: IconButton(
            onPressed: _exportToExcel,
            icon: Icon(Icons.download_rounded, color: Colors.grey[600], size: 22),
            tooltip: '导出Excel',
          ),
        ),
        const SizedBox(width: 8),
        // 筛选按钮
        Container(
          decoration: BoxDecoration(
            color: (_selectedCategories.isNotEmpty || _selectedStatuses.isNotEmpty) 
                ? Colors.blue[50] 
                : Colors.grey[50],
            borderRadius: BorderRadius.circular(12),
            border: Border.all(
              color: (_selectedCategories.isNotEmpty || _selectedStatuses.isNotEmpty) 
                  ? Colors.blue[200]! 
                  : Colors.grey[200]!, 
              width: 1
            ),
          ),
          child: Stack(
            children: [
              IconButton(
                onPressed: _showFilterBottomSheet,
                icon: Icon(
                  Icons.tune_rounded, 
                  color: (_selectedCategories.isNotEmpty || _selectedStatuses.isNotEmpty) 
                      ? Colors.blue[600] 
                      : Colors.grey[600], 
                  size: 22
                ),
                tooltip: '筛选',
              ),
              if (_selectedCategories.isNotEmpty || _selectedStatuses.isNotEmpty)
                Positioned(
                  right: 8,
                  top: 8,
                  child: Container(
                    width: 8,
                    height: 8,
                    decoration: BoxDecoration(
                      color: Colors.red[500],
                      borderRadius: BorderRadius.circular(4),
                    ),
                  ),
                ),
            ],
          ),
        ),
        const SizedBox(width: 8),
        // 批量选择按钮
        Container(
          decoration: BoxDecoration(
            color: Colors.grey[50],
            borderRadius: BorderRadius.circular(12),
            border: Border.all(color: Colors.grey[200]!, width: 1),
          ),
          child: IconButton(
            onPressed: _enterBatchMode,
            icon: Icon(Icons.checklist_rounded, color: Colors.grey[600], size: 22),
            tooltip: '批量选择',
          ),
        ),
        const SizedBox(width: 8),
        // 添加资产按钮
        Container(
          decoration: BoxDecoration(
            gradient: LinearGradient(
              colors: [Colors.blue[600]!, Colors.blue[700]!],
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
            ),
            borderRadius: BorderRadius.circular(12),
            boxShadow: [
              BoxShadow(
                color: Colors.blue.withOpacity(0.3),
                spreadRadius: 0,
                blurRadius: 8,
                offset: const Offset(0, 4),
              ),
            ],
          ),
          child: IconButton(
            onPressed: () => context.go(AppRoutes.assetCreate),
            icon: const Icon(Icons.add_rounded, color: Colors.white, size: 22),
            tooltip: '添加资产',
          ),
        ),
      ],
    );
  }

  // 批量模式工具栏
  Widget _buildBatchModeToolbar() {
    final assetProvider = Provider.of<AssetProvider>(context, listen: false);
    final totalAssets = assetProvider.assets.length;
    final selectedCount = _selectedAssetIds.length;
    final isAllSelected = selectedCount == totalAssets && totalAssets > 0;

    return Row(
      children: [
        // 取消按钮
        TextButton.icon(
          onPressed: _exitBatchMode,
          icon: const Icon(Icons.close_rounded, size: 20),
          label: const Text('取消'),
          style: TextButton.styleFrom(
            foregroundColor: Colors.grey[600],
          ),
        ),
        const SizedBox(width: 16),
        // 选中数量显示
        Expanded(
          child: Text(
            selectedCount > 0 ? '已选择 $selectedCount 项' : '请选择资产',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w600,
              color: selectedCount > 0 ? Colors.blue[600] : Colors.grey[600],
            ),
          ),
        ),
        // 全选按钮
        Container(
          decoration: BoxDecoration(
            color: isAllSelected ? Colors.blue[50] : Colors.grey[50],
            borderRadius: BorderRadius.circular(12),
            border: Border.all(
              color: isAllSelected ? Colors.blue[200]! : Colors.grey[200]!, 
              width: 1
            ),
          ),
          child: IconButton(
            onPressed: totalAssets > 0 ? _selectAllAssets : null,
            icon: Icon(
              isAllSelected ? Icons.check_box_rounded : Icons.check_box_outline_blank_rounded,
              color: isAllSelected ? Colors.blue[600] : Colors.grey[600],
              size: 22,
            ),
            tooltip: isAllSelected ? '取消全选' : '全选',
          ),
        ),
        const SizedBox(width: 8),
        // 删除按钮
        Container(
          decoration: BoxDecoration(
            color: selectedCount > 0 ? Colors.red[50] : Colors.grey[100],
            borderRadius: BorderRadius.circular(12),
            border: Border.all(
              color: selectedCount > 0 ? Colors.red[200]! : Colors.grey[200]!, 
              width: 1
            ),
          ),
          child: IconButton(
            onPressed: selectedCount > 0 ? _batchDeleteAssets : null,
            icon: Icon(
              Icons.delete_rounded,
              color: selectedCount > 0 ? Colors.red[600] : Colors.grey[400],
              size: 22,
            ),
            tooltip: '删除选中项',
          ),
        ),
      ],
    );
  }





  Widget _buildAssetList() {
    return Consumer<AssetProvider>(
      builder: (context, assetProvider, child) {
        if (assetProvider.isLoading && assetProvider.assets.isEmpty) {
          return const Center(
            child: Padding(
              padding: EdgeInsets.all(32),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  CircularProgressIndicator(),
                  SizedBox(height: 16),
                  Text(
                    '正在加载资产数据...',
                    style: TextStyle(color: Colors.grey, fontSize: 14),
                  ),
                ],
              ),
            ),
          );
        }

        if (assetProvider.errorMessage != null) {
          return Center(
            child: Padding(
              padding: const EdgeInsets.all(32),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(Icons.error_outline, size: 48, color: Colors.red[300]),
                  const SizedBox(height: 16),
                  Text(
                    '加载失败',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w500,
                      color: Colors.grey[800],
                    ),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    assetProvider.errorMessage!,
                    style: TextStyle(color: Colors.grey[600], fontSize: 14),
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: 16),
                  ElevatedButton.icon(
                    onPressed: _handleRefresh,
                    icon: const Icon(Icons.refresh),
                    label: const Text('重新加载'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.blue[600],
                      foregroundColor: Colors.white,
                    ),
                  ),
                ],
              ),
            ),
          );
        }

        if (assetProvider.assets.isEmpty) {
          return Center(
            child: Padding(
              padding: const EdgeInsets.all(32),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(Icons.inventory_2_outlined, size: 64, color: Colors.grey[300]),
                  const SizedBox(height: 16),
                  Text(
                    _isSearching ? '没有找到匹配的资产' : '暂无资产数据',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w500,
                      color: Colors.grey[600],
                    ),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    _isSearching ? '尝试调整搜索条件' : '点击上方按钮添加第一个资产',
                    style: TextStyle(color: Colors.grey[500], fontSize: 14),
                  ),
                ],
              ),
            ),
          );
        }

        return RefreshIndicator(
          onRefresh: _handleRefresh,
          child: ListView.builder(
            controller: _scrollController,
            padding: const EdgeInsets.all(16),
            itemCount: assetProvider.assets.length,
            itemBuilder: (context, index) {
              final asset = assetProvider.assets[index];
              return _buildAssetCard(asset);
            },
          ),
        );
      },
    );
  }

  Widget _buildAssetCard(Asset asset) {
    final isSelected = _selectedAssetIds.contains(asset.id);
    
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: _isBatchMode && isSelected ? Colors.blue[300]! : Colors.grey[100]!, 
          width: _isBatchMode && isSelected ? 2 : 1
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.06),
            spreadRadius: 0,
            blurRadius: 12,
            offset: const Offset(0, 4),
          ),
          BoxShadow(
            color: Colors.black.withOpacity(0.02),
            spreadRadius: 0,
            blurRadius: 4,
            offset: const Offset(0, 1),
          ),
        ],
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: _isBatchMode 
              ? () => _toggleAssetSelection(asset.id!)
              : () => context.go('/assets/${asset.id}'),
          onLongPress: !_isBatchMode ? () {
            _enterBatchMode();
            _toggleAssetSelection(asset.id!);
          } : null,
          borderRadius: BorderRadius.circular(16),
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // 资产基本信息行
                Row(
                  children: [
                    // 批量选择模式下的复选框
                    if (_isBatchMode)
                      Container(
                        margin: const EdgeInsets.only(right: 12),
                        child: Container(
                          width: 24,
                          height: 24,
                          decoration: BoxDecoration(
                            color: isSelected ? Colors.blue[600] : Colors.transparent,
                            borderRadius: BorderRadius.circular(6),
                            border: Border.all(
                              color: isSelected ? Colors.blue[600]! : Colors.grey[400]!,
                              width: 2,
                            ),
                          ),
                          child: isSelected
                              ? const Icon(
                                  Icons.check_rounded,
                                  color: Colors.white,
                                  size: 16,
                                )
                              : null,
                        ),
                      ),
                    // 现代化图标
                    Container(
                      width: 48,
                      height: 48,
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(14),
                        gradient: LinearGradient(
                          colors: [
                            _getCategoryColor(asset.category).withOpacity(0.8),
                            _getCategoryColor(asset.category),
                          ],
                          begin: Alignment.topLeft,
                          end: Alignment.bottomRight,
                        ),
                        boxShadow: [
                          BoxShadow(
                            color: _getCategoryColor(asset.category).withOpacity(0.3),
                            spreadRadius: 0,
                            blurRadius: 8,
                            offset: const Offset(0, 4),
                          ),
                        ],
                      ),
                      child: Center(
                        child: FaIcon(
                          _getCategoryIcon(asset.category),
                          color: Colors.white,
                          size: 20,
                        ),
                      ),
                    ),
                    const SizedBox(width: 16),
                    // 资产信息
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            asset.name,
                            style: const TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.w700,
                              color: Colors.black87,
                              letterSpacing: -0.2,
                            ),
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                          ),
                          const SizedBox(height: 4),
                          Text(
                            '编号: ${asset.assetNumber}',
                            style: TextStyle(
                              color: Colors.grey[600],
                              fontSize: 14,
                              fontWeight: FontWeight.w500,
                            ),
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                          ),
                        ],
                      ),
                    ),
                    // 价值和操作
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.end,
                      children: [
                        if (asset.value != null)
                          Container(
                            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                            decoration: BoxDecoration(
                              color: Colors.green[50],
                              borderRadius: BorderRadius.circular(8),
                              border: Border.all(color: Colors.green[200]!, width: 1),
                            ),
                            child: Text(
                              '¥${asset.value!.toStringAsFixed(0)}',
                              style: TextStyle(
                                fontWeight: FontWeight.w700,
                                color: Colors.green[700],
                                fontSize: 13,
                              ),
                            ),
                          ),
                        if (!_isBatchMode) ...[
                          const SizedBox(height: 8),
                          // 操作菜单
                          Container(
                            decoration: BoxDecoration(
                              color: Colors.grey[50],
                              borderRadius: BorderRadius.circular(8),
                            ),
                            child: PopupMenuButton<String>(
                              icon: Icon(Icons.more_horiz_rounded, color: Colors.grey[600], size: 20),
                              padding: EdgeInsets.zero,
                              onSelected: (value) {
                                if (value == 'edit') {
                                  context.go('/assets/${asset.id}/edit');
                                } else if (value == 'delete') {
                                  _showDeleteConfirmation(asset);
                                }
                              },
                              shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
                              itemBuilder: (context) => [
                                const PopupMenuItem(
                                  value: 'edit',
                                  child: Row(
                                    children: [
                                      Icon(Icons.edit_rounded, size: 18),
                                      SizedBox(width: 12),
                                      Text('编辑'),
                                    ],
                                  ),
                                ),
                                const PopupMenuItem(
                                  value: 'delete',
                                  child: Row(
                                    children: [
                                      Icon(Icons.delete_rounded, size: 18, color: Colors.red),
                                      SizedBox(width: 12),
                                      Text('删除', style: TextStyle(color: Colors.red)),
                                    ],
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ],
                      ],
                    ),
                  ],
                ),
                const SizedBox(height: 12),
                // 品牌型号
                if (asset.brand != null && asset.model != null)
                  Container(
                    margin: const EdgeInsets.only(bottom: 8),
                    padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                    decoration: BoxDecoration(
                      color: Colors.grey[50],
                      borderRadius: BorderRadius.circular(10),
                      border: Border.all(color: Colors.grey[200]!, width: 1),
                    ),
                    child: Row(
                      children: [
                        Icon(Icons.info_outline_rounded, color: Colors.grey[600], size: 16),
                        const SizedBox(width: 8),
                        Expanded(
                          child: Text(
                            '${asset.brand} ${asset.model}',
                            style: TextStyle(
                              color: Colors.grey[700],
                              fontSize: 14,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                // 现代化标签行
                Wrap(
                  spacing: 8,
                  runSpacing: 8,
                  children: [
                    _buildModernChip(
                      _getCategoryDisplayName(asset.category), 
                      _getCategoryColor(asset.category),
                      Icons.category_rounded,
                    ),
                    _buildStatusChip(asset.status),
                    if (asset.assignedTo != null && asset.assignedTo!.isNotEmpty)
                      _buildModernChip(
                        asset.assignedTo!, 
                        Colors.indigo,
                        Icons.person_rounded,
                      ),
                  ],
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildModernChip(String value, Color color, IconData icon) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: color.withOpacity(0.2), width: 1),
        boxShadow: [
          BoxShadow(
            color: color.withOpacity(0.1),
            spreadRadius: 0,
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(icon, color: color, size: 14),
          const SizedBox(width: 6),
          Text(
            value,
            style: TextStyle(
              color: color,
              fontSize: 12,
              fontWeight: FontWeight.w600,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildStatusChip(AssetStatus status) {
    Color color;
    IconData icon;
    String displayName = _getStatusDisplayName(status);

    switch (status) {
      case AssetStatus.available:
        color = Colors.green;
        icon = Icons.check_circle_rounded;
        break;
      case AssetStatus.assigned:
        color = Colors.blue;
        icon = Icons.person_rounded;
        break;
      case AssetStatus.maintenance:
        color = Colors.orange;
        icon = Icons.build_rounded;
        break;
      case AssetStatus.retired:
        color = Colors.red;
        icon = Icons.delete_forever_rounded;
        break;
    }

    return _buildModernChip(displayName, color, icon);
  }

  String _getCategoryDisplayName(AssetCategory category) {
    switch (category) {
      case AssetCategory.laptop:
        return '笔记本电脑';
      case AssetCategory.desktop:
        return '台式电脑';
      case AssetCategory.monitor:
        return '显示器';
      case AssetCategory.printer:
        return '打印机';
      case AssetCategory.phone:
        return '手机';
      case AssetCategory.tablet:
        return '平板电脑';
      case AssetCategory.server:
        return '服务器';
      case AssetCategory.network:
        return '网络设备';
      case AssetCategory.other:
        return '其他';
    }
  }

  String _getStatusDisplayName(AssetStatus status) {
    switch (status) {
      case AssetStatus.available:
        return '未分配';
      case AssetStatus.assigned:
        return '已分配';
      case AssetStatus.maintenance:
        return '维护中';
      case AssetStatus.retired:
        return '已报废';
    }
  }

  Color _getCategoryColor(AssetCategory category) {
    switch (category) {
      case AssetCategory.laptop:
        return Colors.blue;
      case AssetCategory.desktop:
        return Colors.indigo;
      case AssetCategory.monitor:
        return Colors.purple;
      case AssetCategory.printer:
        return Colors.teal;
      case AssetCategory.phone:
        return Colors.orange;
      case AssetCategory.tablet:
        return Colors.pink;
      case AssetCategory.server:
        return Colors.red;
      case AssetCategory.network:
        return Colors.green;
      case AssetCategory.other:
        return Colors.grey;
    }
  }

  IconData _getCategoryIcon(AssetCategory category) {
    switch (category) {
      case AssetCategory.laptop:
        return FontAwesomeIcons.laptop;
      case AssetCategory.desktop:
        return FontAwesomeIcons.desktop;
      case AssetCategory.monitor:
        return FontAwesomeIcons.tv;
      case AssetCategory.printer:
        return FontAwesomeIcons.print;
      case AssetCategory.phone:
        return FontAwesomeIcons.mobileScreen;
      case AssetCategory.tablet:
        return FontAwesomeIcons.tablet;
      case AssetCategory.server:
        return FontAwesomeIcons.server;
      case AssetCategory.network:
        return FontAwesomeIcons.wifi;
      case AssetCategory.other:
        return FontAwesomeIcons.cube;
    }
  }

  Future<void> _showDeleteConfirmation(Asset asset) async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('确认删除'),
        content: Text('确定要删除资产 "${asset.name}" 吗？\n此操作无法撤销。'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('取消'),
          ),
          ElevatedButton(
            onPressed: () => Navigator.of(context).pop(true),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.red,
              foregroundColor: Colors.white,
            ),
            child: const Text('删除'),
          ),
        ],
      ),
    );

    if (confirmed == true && mounted) {
      final assetProvider = Provider.of<AssetProvider>(context, listen: false);
      final success = await assetProvider.deleteAsset(asset.id!);
      
      if (success && mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('资产删除成功'),
            backgroundColor: Colors.green,
          ),
        );
      } else if (mounted && assetProvider.errorMessage != null) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('删除失败: ${assetProvider.errorMessage}'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Future<void> _exportToExcel() async {
    final assetProvider = Provider.of<AssetProvider>(context, listen: false);
    
    // 显示保存位置选择对话框
    final saveToDownload = await _showSaveLocationDialog();
    if (saveToDownload == null) return; // 用户取消了选择
    
    try {
      // 显示加载对话框
      showDialog(
        context: context,
        barrierDismissible: false,
        builder: (context) => const AlertDialog(
          content: Row(
            children: [
              CircularProgressIndicator(),
              SizedBox(width: 16),
              Text('正在导出Excel文件...'),
            ],
          ),
        ),
      );

      // 导出Excel
      final filePath = await ExcelService.exportAssetsToExcel(
        assetProvider.assets,
        saveToDownload: saveToDownload,
      );
      
      // 关闭加载对话框
      if (mounted) {
        Navigator.of(context).pop();
        
        // 显示成功消息
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Excel文件已保存到: $filePath'),
            backgroundColor: Colors.green,
            duration: const Duration(seconds: 3),
          ),
        );
      }
    } catch (e) {
      // 关闭加载对话框
      if (mounted) {
        Navigator.of(context).pop();
        
        // 显示错误消息
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('导出失败: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Future<bool?> _showSaveLocationDialog() async {
    return showDialog<bool>(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('选择保存位置'),
          content: const Text('请选择Excel文件的保存位置：'),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(null),
              child: const Text('取消'),
            ),
            TextButton(
              onPressed: () => Navigator.of(context).pop(false),
              child: const Text('应用目录'),
            ),
            TextButton(
              onPressed: () => Navigator.of(context).pop(true),
              child: const Text('Download文件夹'),
            ),
          ],
        );
      },
    );
  }
}
