import 'dart:async';
import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:provider/provider.dart';
import 'package:go_router/go_router.dart';
import '../models/user.dart';
import '../services/user_service.dart';
import '../services/excel_service.dart';
import '../widgets/loading_widget.dart';
import '../widgets/error_widget.dart';
import '../widgets/main_layout.dart';
import '../providers/auth_provider.dart';
import '../config/routes.dart';
import 'user_form_screen.dart';
import 'user_detail_screen.dart';

class UserManagementScreen extends StatefulWidget {
  const UserManagementScreen({Key? key}) : super(key: key);

  @override
  State<UserManagementScreen> createState() => _UserManagementScreenState();
}

class _UserManagementScreenState extends State<UserManagementScreen> {
  final UserService _userService = UserService();
  final TextEditingController _searchController = TextEditingController();
  final ScrollController _scrollController = ScrollController();
  Timer? _debounceTimer;
  
  UserListResponse? _userListResponse;
  bool _isLoading = false;
  String? _error;
  bool _showScrollToTop = false;
  bool _isSearching = false;
  
  // Filter and pagination state
  String? _selectedRole;
  String? _selectedDepartment;
  int _currentPage = 1;
  final int _pageSize = 10;
  String _sortBy = 'CreatedAt';
  bool _sortDescending = true;
  
  List<String> _roles = [];
  List<String> _departments = [];

  @override
  void initState() {
    super.initState();
    // 检查认证状态
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _checkAuthAndInitialize();
    });
    _scrollController.addListener(_onScroll);
    _searchController.addListener(_onSearchChanged);
  }

  @override
  void dispose() {
    _searchController.dispose();
    _scrollController.dispose();
    _debounceTimer?.cancel();
    super.dispose();
  }

  Future<void> _checkAuthAndInitialize() async {
    final authProvider = Provider.of<AuthProvider>(context, listen: false);
    
    // 如果用户未登录，重新初始化认证状态
    if (!authProvider.isLoggedIn) {
      await authProvider.initializeAuth();
    }
    
    // 如果仍然未登录，显示错误信息
    if (!authProvider.isLoggedIn) {
      setState(() {
        _error = '用户未登录，请重新登录';
      });
      return;
    }
    
    // 加载初始数据
    await _loadInitialData();
  }

  Future<void> _loadInitialData() async {
    await Future.wait([
      _loadUsers(),
      _loadRoles(),
      _loadDepartments(),
    ]);
  }

  Future<void> _loadUsers() async {
    setState(() {
      _isLoading = true;
      _error = null;
    });

    try {
      final query = UserQuery(
        search: _searchController.text.isEmpty ? null : _searchController.text,
        role: _selectedRole,
        department: _selectedDepartment,
        pageNumber: _currentPage,
        pageSize: _pageSize,
        sortBy: _sortBy,
        sortDescending: _sortDescending,
      );

      final response = await _userService.getUsers(query);
      
      setState(() {
        _userListResponse = response;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _error = e.toString();
        _isLoading = false;
      });
    }
  }

  Future<void> _loadRoles() async {
    try {
      final roles = await _userService.getRoles();
      setState(() {
        _roles = roles;
      });
    } catch (e) {
      // Handle error silently for roles
    }
  }

  Future<void> _loadDepartments() async {
    try {
      final departments = await _userService.getDepartments();
      setState(() {
        _departments = departments;
      });
    } catch (e) {
      // Handle error silently for departments
    }
  }

  void _onSearchChanged() {
    if (_debounceTimer?.isActive ?? false) _debounceTimer!.cancel();
    _debounceTimer = Timer(const Duration(milliseconds: 500), () {
      _currentPage = 1;
      _loadUsers();
    });
    
    // 更新搜索状态
    setState(() {
      _isSearching = _searchController.text.isNotEmpty;
    });
  }

  void _onFilterChanged() {
    _currentPage = 1;
    _loadUsers();
  }

  void _onPageChanged(int page) {
    setState(() {
      _currentPage = page;
    });
    _loadUsers();
  }

  void _onSortChanged(String sortBy) {
    setState(() {
      if (_sortBy == sortBy) {
        _sortDescending = !_sortDescending;
      } else {
        _sortBy = sortBy;
        _sortDescending = true;
      }
    });
    _loadUsers();
  }

  void _onScroll() {
    if (_scrollController.offset >= 400 && !_showScrollToTop) {
      setState(() {
        _showScrollToTop = true;
      });
    } else if (_scrollController.offset < 400 && _showScrollToTop) {
      setState(() {
        _showScrollToTop = false;
      });
    }
  }

  void _scrollToTop() {
    _scrollController.animateTo(
      0,
      duration: const Duration(milliseconds: 500),
      curve: Curves.easeInOut,
    );
  }

  Future<void> _deleteUser(User user) async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('确认删除'),
        content: Text('确定要删除用户 "${user.username}" 吗？此操作不可撤销。'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('取消'),
          ),
          TextButton(
            onPressed: () => Navigator.of(context).pop(true),
            style: TextButton.styleFrom(foregroundColor: Colors.red),
            child: const Text('删除'),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      try {
        await _userService.deleteUser(user.id);
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('用户删除成功')),
        );
        _loadUsers();
      } catch (e) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('删除失败: $e')),
        );
      }
    }
  }

  void _navigateToUserForm({User? user}) async {
    final result = await Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => UserFormScreen(user: user),
      ),
    );

    if (result == true) {
      _loadUsers();
    }
  }

  void _navigateToUserDetail(User user) async {
    final result = await Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => UserDetailScreen(user: user),
      ),
    );

    if (result == true) {
      _loadUsers();
    }
  }

  Future<void> _showChangeRoleDialog(User user) async {
    String? selectedRole = user.role;
    
    final result = await showDialog<String>(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('更改用户权限 - ${user.username}'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text('当前权限: ${user.role}'),
            const SizedBox(height: 16),
            DropdownButtonFormField<String>(
              value: selectedRole,
              decoration: const InputDecoration(
                labelText: '选择新权限',
                border: OutlineInputBorder(),
              ),
              items: UserRole.all.map((role) => DropdownMenuItem(
                value: role,
                child: Text(role),
              )).toList(),
              onChanged: (value) {
                selectedRole = value;
              },
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('取消'),
          ),
          TextButton(
            onPressed: () => Navigator.of(context).pop(selectedRole),
            child: const Text('确认'),
          ),
        ],
      ),
    );

    if (result != null && result != user.role) {
      await _changeUserRole(user, result);
    }
  }

  Future<void> _changeUserRole(User user, String newRole) async {
    try {
      await _userService.changeUserRole(user.id, newRole);
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('用户 "${user.username}" 权限已更改为 $newRole')),
      );
      _loadUsers();
    } catch (e) {
      String errorMessage = '权限更改失败';
      if (e.toString().contains('登录已过期')) {
        errorMessage = '登录已过期，请重新登录';
        // 自动跳转到登录页面
        Future.delayed(const Duration(seconds: 2), () {
          if (mounted) {
            context.go(AppRoutes.login);
          }
        });
      } else if (e.toString().contains('Unauthorized access')) {
        errorMessage = '权限不足：只有管理员可以修改用户权限';
      } else if (e.toString().contains('Network error')) {
        errorMessage = '网络错误：请检查网络连接';
      } else {
        errorMessage = '权限更改失败: ${e.toString().replaceAll('Exception: ', '')}';
      }
      
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(errorMessage),
          backgroundColor: Colors.red,
          duration: const Duration(seconds: 4),
        ),
      );
    }
  }

  Future<void> _toggleUserStatus(User user) async {
    final action = user.isActive ? '封禁' : '解禁';
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('确认$action'),
        content: Text('确定要${action}用户 "${user.username}" 吗？'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('取消'),
          ),
          TextButton(
            onPressed: () => Navigator.of(context).pop(true),
            style: TextButton.styleFrom(
              foregroundColor: user.isActive ? Colors.red : Colors.green,
            ),
            child: Text(action),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      try {
        await _userService.toggleUserStatus(user.id, !user.isActive);
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('用户 "${user.username}" 已${action}')),
        );
        _loadUsers();
      } catch (e) {
        String errorMessage = '${action}失败';
        if (e.toString().contains('登录已过期')) {
          errorMessage = '登录已过期，请重新登录';
          // 自动跳转到登录页面
          Future.delayed(const Duration(seconds: 2), () {
            if (mounted) {
              context.go(AppRoutes.login);
            }
          });
        } else if (e.toString().contains('Unauthorized access')) {
          errorMessage = '权限不足：只有管理员可以${action}用户';
        } else if (e.toString().contains('Network error')) {
          errorMessage = '网络错误：请检查网络连接';
        } else {
          errorMessage = '${action}失败: ${e.toString().replaceAll('Exception: ', '')}';
        }
        
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(errorMessage),
            backgroundColor: Colors.red,
            duration: const Duration(seconds: 4),
          ),
        );
      }
    }
  }

  Future<void> _exportToExcel() async {
    if (_userListResponse == null || _userListResponse!.users.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('没有用户数据可导出'),
          backgroundColor: Colors.orange,
        ),
      );
      return;
    }

    // 显示保存位置选择对话框
    final saveToDownload = await _showSaveLocationDialog();
    if (saveToDownload == null) return; // 用户取消了选择
    
    try {
      // 显示加载对话框
      showDialog(
        context: context,
        barrierDismissible: false,
        builder: (context) => const AlertDialog(
          content: Row(
            children: [
              CircularProgressIndicator(),
              SizedBox(width: 16),
              Text('正在导出Excel文件...'),
            ],
          ),
        ),
      );

      // 获取所有用户数据（不分页）
      final allUsersQuery = UserQuery(
        search: _searchController.text.isEmpty ? null : _searchController.text,
        role: _selectedRole,
        department: _selectedDepartment,
        pageNumber: 1,
        pageSize: 1000, // 获取大量数据
        sortBy: _sortBy,
        sortDescending: _sortDescending,
      );
      
      final allUsersResponse = await _userService.getUsers(allUsersQuery);

      // 导出Excel
      final filePath = await ExcelService.exportUsersToExcel(
        allUsersResponse.users,
        saveToDownload: saveToDownload,
      );
      
      // 关闭加载对话框
      if (mounted) {
        Navigator.of(context).pop();
        
        // 显示成功消息
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Excel文件已保存到: $filePath'),
            backgroundColor: Colors.green,
            duration: const Duration(seconds: 3),
          ),
        );
      }
    } catch (e) {
      // 关闭加载对话框
      if (mounted) {
        Navigator.of(context).pop();
        
        // 显示错误消息
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('导出失败: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Future<bool?> _showSaveLocationDialog() async {
    return showDialog<bool>(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('选择保存位置'),
          content: const Text('请选择Excel文件的保存位置：'),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(null),
              child: const Text('取消'),
            ),
            TextButton(
              onPressed: () => Navigator.of(context).pop(false),
              child: const Text('应用目录'),
            ),
            TextButton(
              onPressed: () => Navigator.of(context).pop(true),
              child: const Text('Download文件夹'),
            ),
          ],
        );
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: MainLayout(
        currentRoute: '/users',
        child: Column(
          children: [
            // 紧凑的工具栏
            Container(
              padding: const EdgeInsets.fromLTRB(16, 8, 16, 16),
              decoration: BoxDecoration(
                color: Colors.white,
                boxShadow: [
                  BoxShadow(
                    color: Colors.grey.withOpacity(0.08),
                    spreadRadius: 1,
                    blurRadius: 3,
                    offset: const Offset(0, 1),
                  ),
                ],
              ),
              child: Column(
                children: [
                  // 操作按钮行
                  Consumer<AuthProvider>(
                    builder: (context, authProvider, child) {
                      final currentUser = authProvider.currentUser;
                      
                      // 只有管理员才能看到添加用户和导出按钮
                      if (currentUser == null || !currentUser.isAdmin) {
                        return const SizedBox.shrink();
                      }
                      
                      return Row(
                        children: [
                          Expanded(
                            child: ElevatedButton.icon(
                              onPressed: () => _navigateToUserForm(),
                              icon: const Icon(Icons.person_add, size: 18),
                              label: const Text('添加用户'),
                              style: ElevatedButton.styleFrom(
                                backgroundColor: Colors.blue[600],
                                foregroundColor: Colors.white,
                                padding: const EdgeInsets.symmetric(vertical: 12),
                                shape: RoundedRectangleBorder(
                                  borderRadius: BorderRadius.circular(10),
                                ),
                              ),
                            ),
                          ),
                          const SizedBox(width: 12),
                          ElevatedButton.icon(
                            onPressed: _exportToExcel,
                            icon: const Icon(Icons.file_download, size: 18),
                            label: const Text('导出'),
                            style: ElevatedButton.styleFrom(
                              backgroundColor: Colors.green[600],
                              foregroundColor: Colors.white,
                              padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 16),
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(10),
                              ),
                            ),
                          ),
                        ],
                      );
                    },
                  ),
                  const SizedBox(height: 16),
                  // 搜索和筛选器
                  _buildSearchAndFilters(),
                ],
              ),
            ),
            // 用户列表
            Expanded(
              child: _buildUserList(),
            ),
            // 分页
            if (_userListResponse != null) _buildPagination(),
          ],
        ),
      ),
      // 回到顶部按钮
      floatingActionButton: _showScrollToTop
          ? FloatingActionButton.small(
              onPressed: _scrollToTop,
              backgroundColor: Colors.blue[600],
              child: const Icon(Icons.keyboard_arrow_up, color: Colors.white, size: 20),
            )
          : null,
    );
  }

  Widget _buildSearchAndFilters() {
    return Column(
      children: [
        // 搜索栏
        Container(
          decoration: BoxDecoration(
            color: Colors.grey[50],
            borderRadius: BorderRadius.circular(12),
            border: Border.all(color: Colors.grey[200]!),
          ),
          child: TextField(
            controller: _searchController,
            decoration: InputDecoration(
              hintText: '搜索用户名、邮箱、姓名...',
              hintStyle: TextStyle(color: Colors.grey[400], fontSize: 14),
              prefixIcon: _isSearching 
                  ? SizedBox(
                      width: 20,
                      height: 20,
                      child: Padding(
                        padding: const EdgeInsets.all(14),
                        child: CircularProgressIndicator(
                          strokeWidth: 2,
                          valueColor: AlwaysStoppedAnimation<Color>(Colors.blue[600]!),
                        ),
                      ),
                    )
                  : Icon(Icons.search, color: Colors.grey[400], size: 20),
              suffixIcon: _searchController.text.isNotEmpty
                  ? IconButton(
                      icon: Icon(Icons.clear, color: Colors.grey[400], size: 20),
                      onPressed: () {
                        _searchController.clear();
                      },
                    )
                  : null,
              border: InputBorder.none,
              contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 14),
            ),
            onChanged: (value) {
              setState(() {}); // 更新UI
            },
          ),
        ),
        const SizedBox(height: 12),
        // 筛选器 - 带图标说明
        Row(
          children: [
            Expanded(
              child: _buildFilterDropdown(
                value: _selectedRole,
                hint: '角色',
                icon: Icons.admin_panel_settings,
                items: [
                  const DropdownMenuItem<String>(value: null, child: Text('全部角色')),
                  ..._roles.map((role) => DropdownMenuItem<String>(
                    value: role,
                    child: Text(role == 'Admin' ? '管理员' : '普通用户'),
                  )),
                ],
                onChanged: (value) {
                  setState(() {
                    _selectedRole = value;
                  });
                  _onFilterChanged();
                },
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: _buildFilterDropdown(
                value: _selectedDepartment,
                hint: '部门',
                icon: Icons.business,
                items: [
                  const DropdownMenuItem<String>(value: null, child: Text('全部部门')),
                  ..._departments.map((dept) => DropdownMenuItem<String>(
                    value: dept,
                    child: Text(dept),
                  )),
                ],
                onChanged: (value) {
                  setState(() {
                    _selectedDepartment = value;
                  });
                  _onFilterChanged();
                },
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildFilterDropdown<T>({
    required T? value,
    required String hint,
    required IconData icon,
    required List<DropdownMenuItem<T>> items,
    required void Function(T?) onChanged,
  }) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(10),
        border: Border.all(color: Colors.grey[200]!, width: 1),
      ),
      child: DropdownButtonHideUnderline(
        child: DropdownButton<T>(
          value: value,
          isExpanded: true,
          hint: Row(
            children: [
              Icon(icon, color: Colors.grey[400], size: 16),
              const SizedBox(width: 8),
              Text(
                hint,
                style: TextStyle(color: Colors.grey[600], fontSize: 13),
              ),
            ],
          ),
          icon: Icon(Icons.keyboard_arrow_down, color: Colors.grey[400], size: 18),
          padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 10),
          items: items,
          onChanged: onChanged,
          style: const TextStyle(color: Colors.black87, fontSize: 13),
        ),
      ),
    );
  }

  Widget _buildUserList() {
    if (_isLoading && (_userListResponse == null || _userListResponse!.users.isEmpty)) {
      return const Center(
        child: Padding(
          padding: EdgeInsets.all(32),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              CircularProgressIndicator(),
              SizedBox(height: 16),
              Text(
                '正在加载用户数据...',
                style: TextStyle(color: Colors.grey, fontSize: 14),
              ),
            ],
          ),
        ),
      );
    }

    if (_error != null) {
      return Center(
        child: Padding(
          padding: const EdgeInsets.all(32),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(Icons.error_outline, size: 48, color: Colors.red[300]),
              const SizedBox(height: 16),
              Text(
                '加载失败',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w500,
                  color: Colors.grey[800],
                ),
              ),
              const SizedBox(height: 8),
              Text(
                _error!,
                style: TextStyle(color: Colors.grey[600], fontSize: 14),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 16),
              Consumer<AuthProvider>(
                builder: (context, authProvider, child) {
                  if (!authProvider.isLoggedIn) {
                    return ElevatedButton.icon(
                      onPressed: () async {
                        await authProvider.initializeAuth();
                        if (authProvider.isLoggedIn) {
                          _loadUsers();
                        } else {
                          // 跳转到登录页面
                          if (mounted) {
                            context.go(AppRoutes.login);
                          }
                        }
                      },
                      icon: const Icon(Icons.login),
                      label: const Text('重新登录'),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.blue[600],
                        foregroundColor: Colors.white,
                      ),
                    );
                  } else {
                    return ElevatedButton.icon(
                      onPressed: _loadUsers,
                      icon: const Icon(Icons.refresh),
                      label: const Text('重新加载'),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.blue[600],
                        foregroundColor: Colors.white,
                      ),
                    );
                  }
                },
              ),
            ],
          ),
        ),
      );
    }

    if (_userListResponse == null || _userListResponse!.users.isEmpty) {
      return Center(
        child: Padding(
          padding: const EdgeInsets.all(32),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(Icons.people_outline, size: 64, color: Colors.grey[300]),
              const SizedBox(height: 16),
              Text(
                _isSearching ? '没有找到匹配的用户' : '暂无用户数据',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w500,
                  color: Colors.grey[600],
                ),
              ),
              const SizedBox(height: 8),
              Text(
                _isSearching ? '尝试调整搜索条件' : '点击上方按钮添加第一个用户',
                style: TextStyle(color: Colors.grey[500], fontSize: 14),
              ),
            ],
          ),
        ),
      );
    }

    return ListView.builder(
      controller: _scrollController,
      padding: const EdgeInsets.all(16),
      itemCount: _userListResponse!.users.length,
      itemBuilder: (context, index) {
        final user = _userListResponse!.users[index];
        return _buildUserCard(user);
      },
    );
  }

  Widget _buildUserCard(User user) {
    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.grey[200]!, width: 0.5),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.08),
            spreadRadius: 0,
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: InkWell(
        onTap: () => _navigateToUserDetail(user),
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // 用户基本信息行
              Row(
                children: [
                  // 头像
                  Container(
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(10),
                      gradient: LinearGradient(
                        colors: user.isActive 
                            ? [Colors.blue[400]!, Colors.blue[600]!]
                            : [Colors.grey[400]!, Colors.grey[600]!],
                        begin: Alignment.topLeft,
                        end: Alignment.bottomRight,
                      ),
                    ),
                    child: CircleAvatar(
                      radius: 20,
                      backgroundColor: Colors.transparent,
                      child: Text(
                        user.username.isNotEmpty ? user.username[0].toUpperCase() : 'U',
                        style: const TextStyle(
                          color: Colors.white,
                          fontWeight: FontWeight.w600,
                          fontSize: 14,
                        ),
                      ),
                    ),
                  ),
                  const SizedBox(width: 12),
                  // 用户信息
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          children: [
                            Expanded(
                              child: Text(
                                user.fullName ?? user.username,
                                style: TextStyle(
                                  fontSize: 15,
                                  fontWeight: FontWeight.w600,
                                  color: user.isActive ? Colors.black87 : Colors.grey[600],
                                ),
                                maxLines: 1,
                                overflow: TextOverflow.ellipsis,
                              ),
                            ),
                            // 状态标签
                            if (!user.isActive)
                              Container(
                                padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                                decoration: BoxDecoration(
                                  color: Colors.red[50],
                                  border: Border.all(color: Colors.red[200]!, width: 0.5),
                                  borderRadius: BorderRadius.circular(4),
                                ),
                                child: Text(
                                  '已封禁',
                                  style: TextStyle(
                                    color: Colors.red[600],
                                    fontSize: 10,
                                    fontWeight: FontWeight.w500,
                                  ),
                                ),
                              ),
                          ],
                        ),
                        const SizedBox(height: 2),
                        Text(
                          user.email,
                          style: TextStyle(
                            color: user.isActive ? Colors.grey[600] : Colors.grey[500],
                            fontSize: 13,
                            height: 1.2,
                          ),
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ],
                    ),
                  ),
                  // 操作菜单
                  Consumer<AuthProvider>(
                    builder: (context, authProvider, child) {
                      final currentUser = authProvider.currentUser;
                      
                      // 只有管理员才能看到操作菜单
                      if (currentUser == null || !currentUser.isAdmin) {
                        return const SizedBox(width: 24);
                      }
                      
                      final isCurrentUser = currentUser.id == user.id;
                      final isTargetAdmin = user.role == UserRole.admin;
                      
                      // 不能对自己或其他管理员进行某些操作
                      final canChangeRole = !isCurrentUser && !isTargetAdmin;
                      final canToggleStatus = !isCurrentUser && !isTargetAdmin;
                      final canDelete = !isCurrentUser && !isTargetAdmin;
                      final hasAnyAction = canChangeRole || canToggleStatus || canDelete;
                      
                      if (!hasAnyAction) {
                        return const SizedBox(width: 24);
                      }
                      
                      return PopupMenuButton<String>(
                        icon: Icon(Icons.more_horiz, color: Colors.grey[500], size: 18),
                        padding: EdgeInsets.zero,
                        onSelected: (value) {
                          switch (value) {
                            case 'change_role':
                              _showChangeRoleDialog(user);
                              break;
                            case 'toggle_status':
                              _toggleUserStatus(user);
                              break;
                            case 'delete':
                              _deleteUser(user);
                              break;
                          }
                        },
                        itemBuilder: (context) => [
                          if (canChangeRole)
                            const PopupMenuItem(
                              value: 'change_role',
                              child: Row(
                                children: [
                                  Icon(Icons.admin_panel_settings, size: 18),
                                  SizedBox(width: 8),
                                  Text('更改权限'),
                                ],
                              ),
                            ),
                          if (canToggleStatus)
                            PopupMenuItem(
                              value: 'toggle_status',
                              child: Row(
                                children: [
                                  Icon(
                                    user.isActive ? Icons.block : Icons.check_circle,
                                    size: 18,
                                    color: user.isActive ? Colors.red : Colors.green,
                                  ),
                                  const SizedBox(width: 8),
                                  Text(
                                    user.isActive ? '封禁用户' : '解禁用户',
                                    style: TextStyle(
                                      color: user.isActive ? Colors.red : Colors.green,
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          if (canDelete)
                            const PopupMenuItem(
                              value: 'delete',
                              child: Row(
                                children: [
                                  Icon(Icons.delete, size: 18, color: Colors.red),
                                  SizedBox(width: 8),
                                  Text('删除', style: TextStyle(color: Colors.red)),
                                ],
                              ),
                            ),
                        ],
                      );
                    },
                  ),
                ],
              ),
              const SizedBox(height: 12),
              // 标签行 - 优化布局
              Row(
                children: [
                  _buildCompactChip('角色', user.role, _getRoleColor(user.role)),
                  const SizedBox(width: 6),
                  if (user.department != null && user.department!.isNotEmpty) ...[
                    _buildCompactChip('部门', user.department!, Colors.indigo),
                    const SizedBox(width: 6),
                  ],
                  const Spacer(),
                  _buildCompactChip('资产', user.assignedAssetsCount.toString(), Colors.amber[700]!),
                ],
              ),
              const SizedBox(height: 8),
              // 创建时间
              Text(
                '创建于 ${DateFormat('MM-dd HH:mm').format(user.createdAt)}',
                style: TextStyle(
                  color: Colors.grey[500],
                  fontSize: 11,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildCompactChip(String label, String value, Color color) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: color.withOpacity(0.3), width: 0.5),
      ),
      child: Text(
        '$label: $value',
        style: TextStyle(
          color: color,
          fontSize: 11,
          fontWeight: FontWeight.w500,
        ),
      ),
    );
  }

  Color _getRoleColor(String role) {
    switch (role.toLowerCase()) {
      case 'admin':
        return Colors.red;
      case 'normal user':
        return Colors.blue;
      default:
        return Colors.grey;
    }
  }

  Widget _buildPagination() {
    final response = _userListResponse!;
    final totalPages = response.totalPages;
    
    if (totalPages <= 1) return const SizedBox.shrink();

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.2),
            spreadRadius: 1,
            blurRadius: 4,
            offset: const Offset(0, -2),
          ),
        ],
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            '共 ${response.totalCount} 条记录',
            style: TextStyle(color: Colors.grey[600]),
          ),
          Row(
            children: [
              IconButton(
                onPressed: _currentPage > 1 ? () => _onPageChanged(_currentPage - 1) : null,
                icon: const Icon(Icons.chevron_left),
              ),
              Text('$_currentPage / $totalPages'),
              IconButton(
                onPressed: _currentPage < totalPages ? () => _onPageChanged(_currentPage + 1) : null,
                icon: const Icon(Icons.chevron_right),
              ),
            ],
          ),
        ],
      ),
    );
  }
} 