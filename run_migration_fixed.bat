@echo off
echo Fixed compilation issues, now running migration...

cd Backend\ITAssetAPI

echo Step 1: Building project...
dotnet build

if %ERRORLEVEL% NEQ 0 (
    echo Build failed! Please check the error messages above.
    pause
    exit /b %ERRORLEVEL%
)

echo Step 2: Checking existing migrations...
if exist "Migrations\*AddUserIsActiveField*" (
    echo Migration already exists, skipping creation...
) else (
    echo Creating migration...
    dotnet ef migrations add AddUserIsActiveField
)

echo Step 3: Updating database...
dotnet ef database update

if %ERRORLEVEL% NEQ 0 (
    echo Database update failed! Please check the error messages above.
    pause
    exit /b %ERRORLEVEL%
)

echo Migration completed successfully!
echo.
echo The IsActive field has been added to the Users table.
echo All existing users are marked as active by default.
echo.
pause 