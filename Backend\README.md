# IT Asset Management API

这是一个基于 .NET Core 6 的 IT 资产管理 API 后端服务，使用 PostgreSQL 数据库。

## 技术栈

- .NET Core 6
- Entity Framework Core
- PostgreSQL
- JWT 身份验证
- BCrypt 密码加密
- Swagger API 文档

## 前置要求

1. .NET 6 SDK
2. PostgreSQL 数据库

## 快速开始

### 1. 安装 PostgreSQL

确保 PostgreSQL 已安装并运行在默认端口 5432。

### 2. 配置数据库

默认连接字符串配置在 `appsettings.json` 中：
```json
{
  "ConnectionStrings": {
    "DefaultConnection": "Host=localhost;Database=ITAssetDB;Username=********;Password=********"
  }
}
```

### 3. 运行项目

```bash
cd Backend/ITAssetAPI
dotnet restore
dotnet run
```

### 4. 访问 API

- API 基础地址: `http://localhost:5000`
- Swagger 文档: `http://localhost:5000/swagger`

## 默认用户账户

系统会自动创建以下测试用户：

| 用户名 | 密码 | 角色 |
|-------|------|------|
| admin | admin123 | Admin |
| user | user123 | Normal User |

## API 端点

### 认证 (Authentication)
- `POST /api/auth/login` - 用户登录
- `GET /api/auth/me` - 获取当前用户信息

### 资产管理 (Assets)
- `GET /api/assets` - 获取资产列表（支持分页、搜索、筛选）
- `GET /api/assets/{id}` - 获取指定资产详情
- `POST /api/assets` - 创建新资产
- `PUT /api/assets/{id}` - 更新资产信息
- `DELETE /api/assets/{id}` - 删除资产

## 查询参数示例

获取资产列表：
```
GET /api/assets?page=1&limit=20&search=MacBook&category=laptop&status=assigned
```

## 数据模型

### 资产状态 (AssetStatus)
- `Available` - 可用
- `Assigned` - 已分配
- `Maintenance` - 维护中
- `Retired` - 已退役

### 资产类别 (AssetCategory)
- `Laptop` - 笔记本电脑
- `Desktop` - 台式电脑
- `Monitor` - 显示器
- `Printer` - 打印机
- `Phone` - 手机
- `Tablet` - 平板电脑
- `Server` - 服务器
- `Network` - 网络设备
- `Other` - 其他

## 开发说明

### 数据库迁移

如果需要重新创建数据库：
```bash
dotnet ef database drop
dotnet ef database update
```

### 添加新的迁移
```bash
dotnet ef migrations add MigrationName
dotnet ef database update
```

## CORS 配置

API 已配置 CORS 以允许来自 Flutter 开发服务器的请求：
- `http://localhost:3000`
- `http://localhost:8080`

## 安全说明

- 所有资产相关的 API 端点都需要 JWT Token 认证
- 密码使用 BCrypt 进行安全加密
- JWT Token 有效期为 24 小时

## 项目结构

```
ITAssetAPI/
├── Controllers/         # API 控制器
├── Data/               # 数据库上下文和种子数据
├── DTOs/               # 数据传输对象
├── Models/             # 实体模型
├── Services/           # 业务逻辑服务
├── Program.cs          # 应用程序入口
└── appsettings.json    # 配置文件
``` 