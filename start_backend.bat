@echo off
echo 启动IT资产管理后端服务...
echo.

cd Backend\ITAssetAPI

echo 检查.NET环境...
dotnet --version
if %errorlevel% neq 0 (
    echo 错误: 未找到.NET SDK，请先安装.NET 6.0或更高版本
    pause
    exit /b 1
)

echo.
echo 还原NuGet包...
dotnet restore

echo.
echo 构建项目...
dotnet build

echo.
echo 启动服务器...
echo 服务将在以下地址运行:
echo   - Web端: http://localhost:5000
echo   - 移动端: http://********:5000 (Android模拟器)
echo   - 局域网: http://0.0.0.0:5000
echo 按 Ctrl+C 停止服务
echo.

dotnet run --urls="http://0.0.0.0:5000"

pause 