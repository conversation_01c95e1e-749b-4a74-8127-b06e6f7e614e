@echo off
echo 修复IT资产管理数据库问题...
echo.

cd Backend\ITAssetAPI

echo 检查.NET环境...
dotnet --version
if %errorlevel% neq 0 (
    echo 错误: 未找到.NET SDK，请先安装.NET 6.0或更高版本
    pause
    exit /b 1
)

echo.
echo 检查Entity Framework工具...
dotnet tool list -g | findstr "dotnet-ef" >nul
if %errorlevel% neq 0 (
    echo 安装Entity Framework工具...
    dotnet tool install --global dotnet-ef
)

echo.
echo 还原NuGet包...
dotnet restore

echo.
echo 构建项目...
dotnet build
if %errorlevel% neq 0 (
    echo 构建失败，请检查代码错误
    pause
    exit /b 1
)

echo.
echo 删除现有迁移（如果存在）...
if exist "Migrations" (
    rmdir /s /q Migrations
    echo 已删除现有迁移文件夹
)

echo.
echo 创建新的数据库迁移...
dotnet ef migrations add InitialCreateWithActivityLogs
if %errorlevel% neq 0 (
    echo 创建迁移失败
    pause
    exit /b 1
)

echo.
echo 删除现有数据库（如果存在）...
dotnet ef database drop --force
echo 已删除现有数据库

echo.
echo 创建新数据库...
dotnet ef database update
if %errorlevel% neq 0 (
    echo 数据库更新失败
    pause
    exit /b 1
)

echo.
echo 创建新的迁移 - 添加活动日志次要类型字段
dotnet ef migrations add AddActivityLogSecondaryTypes

echo.
echo 应用迁移到数据库
dotnet ef database update

echo.
echo 数据库修复完成！
echo ActivityLogs表已成功创建
echo.
echo 现在可以运行 start_backend.bat 启动服务器

pause 