using ITAssetAPI.DTOs;
using ITAssetAPI.Models;

namespace ITAssetAPI.Services
{
    public interface IAssetService
    {
        Task<AssetListResponseDto> GetAssetsAsync(int page = 1, int pageSize = 20, string? search = null, string? category = null, string? status = null);
        Task<AssetDto?> GetAssetByIdAsync(int id);
        Task<AssetDto> CreateAssetAsync(CreateAssetDto createAssetDto);
        Task<AssetDto?> UpdateAssetAsync(int id, UpdateAssetDto updateAssetDto);
        Task<bool> DeleteAssetAsync(int id);
        Task<string> GenerateNextAssetNumberAsync(AssetCategory category);
    }
} 