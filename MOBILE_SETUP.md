# 移动端运行设置说明

## 问题描述
当在Android虚拟机中运行应用时，出现连接错误：`Connection refused`

## 原因
Android虚拟机无法直接访问宿主机的`localhost`，需要使用特殊的IP地址。

## 解决方案

### 方案1：启动后端服务器（推荐）

1. **启动后端API服务器**
   ```bash
   # 在项目根目录运行
   start_backend.bat
   ```
   
   或者手动启动：
   ```bash
   cd Backend\ITAssetAPI
   dotnet run --urls="http://0.0.0.0:5000"
   ```

2. **确认服务器运行**
   - 看到类似输出：`Now listening on: http://0.0.0.0:5000`
   - 在浏览器访问 `http://localhost:5000` 确认服务正常

3. **运行Flutter应用**
   ```bash
   # Web端
   flutter run -d chrome --debug
   
   # Android虚拟机
   flutter run -d android --debug
   ```

### 方案2：使用模拟数据（临时方案）

如果无法启动后端服务器，可以临时使用模拟数据：

1. **修改API服务配置**
   在 `lib/services/api_service.dart` 中：
   ```dart
   static const bool useMockData = true; // 改为true
   ```

2. **重新运行应用**
   ```bash
   flutter run -d android --debug
   ```

3. **使用模拟账户登录**
   - 用户名：`admin`，密码：`admin123`
   - 用户名：`user`，密码：`user123`

## 网络配置说明

### Android模拟器网络映射
- `localhost` → 模拟器本身
- `********` → 宿主机的localhost
- `********` → 宿主机的网关

### 应用自动配置
应用已配置为根据平台自动选择正确的API地址：
- **Web端**：`http://localhost:5000/api`
- **移动端**：`http://********:5000/api`

## 故障排除

### 1. 后端服务器无法启动
- 确保已安装.NET 6.0或更高版本
- 检查端口5000是否被占用
- 查看控制台错误信息

### 2. 移动端仍然连接失败
- 确认后端服务器使用`--urls="http://0.0.0.0:5000"`启动
- 检查防火墙设置
- 尝试重启Android虚拟机

### 3. 数据库连接问题
- 运行 `setup_database.bat` 初始化数据库
- 检查PostgreSQL服务是否运行
- 查看后端控制台的数据库连接日志

## 测试连接

### 测试后端API
在浏览器或Postman中访问：
```
http://localhost:5000/api/auth/users
```

### 测试移动端连接
在Android虚拟机的浏览器中访问：
```
http://********:5000/api/auth/users
```

如果返回401错误（未授权），说明连接正常，只是需要登录。 