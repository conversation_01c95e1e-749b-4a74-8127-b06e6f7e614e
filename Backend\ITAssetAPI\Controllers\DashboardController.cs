using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using ITAssetAPI.Data;

namespace ITAssetAPI.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    [Authorize]
    public class DashboardController : ControllerBase
    {
        private readonly ApplicationDbContext _context;

        public DashboardController(ApplicationDbContext context)
        {
            _context = context;
        }

        [HttpGet("stats")]
        public async Task<ActionResult> GetDashboardStats()
        {
            try
            {
                var totalAssets = await _context.Assets.CountAsync();
                var assignedAssets = await _context.Assets.CountAsync(a => a.Status == Models.AssetStatus.Assigned);
                var availableAssets = await _context.Assets.CountAsync(a => a.Status == Models.AssetStatus.Available);
                var maintenanceAssets = await _context.Assets.CountAsync(a => a.Status == Models.AssetStatus.Maintenance);
                var retiredAssets = await _context.Assets.CountAsync(a => a.Status == Models.AssetStatus.Retired);

                // 按类别统计
                var categoryStats = await _context.Assets
                    .GroupBy(a => a.Category)
                    .Select(g => new
                    {
                        category = g.Key.ToString(),
                        count = g.Count()
                    })
                    .ToListAsync();

                // 按月份统计（简化版，只返回基础数据）
                var monthlyStats = new[]
                {
                    new { month = "2025-01", purchased = 5, retired = 1 },
                    new { month = "2025-02", purchased = 3, retired = 0 },
                    new { month = "2025-03", purchased = 4, retired = 2 },
                    new { month = "2025-04", purchased = 6, retired = 1 },
                    new { month = "2025-05", purchased = 2, retired = 0 }
                };

                var stats = new
                {
                    totalAssets,
                    assignedAssets,
                    availableAssets,
                    maintenanceAssets,
                    retiredAssets,
                    categoryStats,
                    monthlyStats
                };

                return Ok(stats);
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { message = "Internal server error", details = ex.Message });
            }
        }
    }
} 