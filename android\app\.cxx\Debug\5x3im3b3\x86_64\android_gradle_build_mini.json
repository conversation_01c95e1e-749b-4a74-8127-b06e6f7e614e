{"buildFiles": ["D:\\flutter\\packages\\flutter_tools\\gradle\\src\\main\\groovy\\CMakeLists.txt"], "cleanCommandsComponents": [["D:\\Android\\SDK\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "D:\\Android\\StudioProject\\ITAssetApp\\android\\app\\.cxx\\Debug\\5x3im3b3\\x86_64", "clean"]], "buildTargetsCommandComponents": ["D:\\Android\\SDK\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "D:\\Android\\StudioProject\\ITAssetApp\\android\\app\\.cxx\\Debug\\5x3im3b3\\x86_64", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {}}