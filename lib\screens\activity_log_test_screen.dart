import 'package:flutter/material.dart';
import '../services/api_service.dart';
import 'package:dio/dio.dart';

class ActivityLogTestScreen extends StatefulWidget {
  const ActivityLogTestScreen({super.key});

  @override
  State<ActivityLogTestScreen> createState() => _ActivityLogTestScreenState();
}

class _ActivityLogTestScreenState extends State<ActivityLogTestScreen> {
  final ApiService _apiService = ApiService();
  bool _isLoading = false;
  String? _error;
  String _result = '';

  Future<void> _testConnection() async {
    setState(() {
      _isLoading = true;
      _error = null;
      _result = '';
    });

    try {
      print('测试基础连接...');
      final dio = Dio();
      
      // 测试基础连接
      final response = await dio.get('http://localhost:5000/api');
      setState(() {
        _result = '基础连接成功\n状态码: ${response.statusCode}\n响应: ${response.data}';
        _isLoading = false;
      });
    } catch (e) {
      print('基础连接失败: $e');
      setState(() {
        _error = '基础连接失败: $e';
        _isLoading = false;
      });
    }
  }

  Future<void> _testActivityLogs() async {
    setState(() {
      _isLoading = true;
      _error = null;
      _result = '';
    });

    try {
      print('开始测试活动日志API...');
      final response = await _apiService.getActivityLogs(page: 1, limit: 10);
      
      setState(() {
        _result = '成功获取 ${response.activityLogs.length} 条活动日志\n'
                 '总数: ${response.totalCount}\n'
                 '总页数: ${response.totalPages}';
        _isLoading = false;
      });
    } catch (e) {
      print('测试失败: $e');
      setState(() {
        _error = e.toString();
        _isLoading = false;
      });
    }
  }

  Future<void> _testDirectAPI() async {
    setState(() {
      _isLoading = true;
      _error = null;
      _result = '';
    });

    try {
      print('直接测试ActivityLogs API...');
      final dio = Dio();
      
      // 添加认证头（如果有token）
      final options = Options();
      // 这里可能需要添加认证头
      
      final response = await dio.get(
        'http://localhost:5000/api/ActivityLogs?page=1&limit=10',
        options: options,
      );
      
      setState(() {
        _result = '直接API调用成功\n状态码: ${response.statusCode}\n'
                 '响应数据: ${response.data.toString().substring(0, 200)}...';
        _isLoading = false;
      });
    } catch (e) {
      print('直接API调用失败: $e');
      setState(() {
        _error = '直接API调用失败: $e';
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('活动日志测试'),
        backgroundColor: Colors.blue[600],
        foregroundColor: Colors.white,
      ),
      body: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            ElevatedButton(
              onPressed: _isLoading ? null : _testConnection,
              child: const Text('测试基础连接'),
            ),
            const SizedBox(height: 8),
            ElevatedButton(
              onPressed: _isLoading ? null : _testDirectAPI,
              child: const Text('直接测试ActivityLogs API'),
            ),
            const SizedBox(height: 8),
            ElevatedButton(
              onPressed: _isLoading ? null : _testActivityLogs,
              child: _isLoading 
                ? const Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      SizedBox(
                        width: 16,
                        height: 16,
                        child: CircularProgressIndicator(strokeWidth: 2),
                      ),
                      SizedBox(width: 8),
                      Text('测试中...'),
                    ],
                  )
                : const Text('测试活动日志API（通过ApiService）'),
            ),
            const SizedBox(height: 20),
            if (_error != null) ...[
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.red[50],
                  border: Border.all(color: Colors.red[300]!),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      '错误信息:',
                      style: TextStyle(
                        fontWeight: FontWeight.bold,
                        color: Colors.red,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Text(
                      _error!,
                      style: const TextStyle(color: Colors.red),
                    ),
                  ],
                ),
              ),
            ],
            if (_result.isNotEmpty) ...[
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.green[50],
                  border: Border.all(color: Colors.green[300]!),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      '测试结果:',
                      style: TextStyle(
                        fontWeight: FontWeight.bold,
                        color: Colors.green,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Text(
                      _result,
                      style: const TextStyle(color: Colors.green),
                    ),
                  ],
                ),
              ),
            ],
            const SizedBox(height: 20),
            const Text(
              '调试信息:',
              style: TextStyle(fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 8),
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.grey[100],
                border: Border.all(color: Colors.grey[300]!),
                borderRadius: BorderRadius.circular(8),
              ),
              child: const Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text('API基础URL: http://localhost:5000/api'),
                  Text('ActivityLogs端点: /ActivityLogs'),
                  Text('完整URL: http://localhost:5000/api/ActivityLogs'),
                  SizedBox(height: 8),
                  Text('请确保:'),
                  Text('1. 后端服务正在运行'),
                  Text('2. 端口5000可访问'),
                  Text('3. ActivityLogsController已正确注册'),
                  Text('4. 数据库连接正常'),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
} 