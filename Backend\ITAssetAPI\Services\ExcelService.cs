using OfficeOpenXml;
using ITAssetAPI.Models;

namespace ITAssetAPI.Services
{
    public class ExcelService : IExcelService
    {
        public async Task<byte[]> ExportAssetsToExcelAsync(List<Asset> assets)
        {
            ExcelPackage.LicenseContext = LicenseContext.NonCommercial;
            
            using var package = new ExcelPackage();
            var worksheet = package.Workbook.Worksheets.Add("资产列表");

            // 设置表头
            var headers = new[]
            {
                "ID", "资产名称", "资产编号", "类别", "状态", "品牌", "型号", 
                "序列号", "分配给", "购买日期", "价值", "供应商", "描述", 
                "位置", "上次维护日期", "下次维护日期", "创建时间", "更新时间"
            };

            for (int i = 0; i < headers.Length; i++)
            {
                worksheet.Cells[1, i + 1].Value = headers[i];
                worksheet.Cells[1, i + 1].Style.Font.Bold = true;
                worksheet.Cells[1, i + 1].Style.Fill.PatternType = OfficeOpenXml.Style.ExcelFillStyle.Solid;
                worksheet.Cells[1, i + 1].Style.Fill.BackgroundColor.SetColor(System.Drawing.Color.LightBlue);
            }

            // 填充数据
            for (int i = 0; i < assets.Count; i++)
            {
                var asset = assets[i];
                var row = i + 2;

                worksheet.Cells[row, 1].Value = asset.Id;
                worksheet.Cells[row, 2].Value = asset.Name;
                worksheet.Cells[row, 3].Value = asset.AssetNumber;
                worksheet.Cells[row, 4].Value = asset.Category.ToString();
                worksheet.Cells[row, 5].Value = asset.Status.ToString();
                worksheet.Cells[row, 6].Value = asset.Brand;
                worksheet.Cells[row, 7].Value = asset.Model;
                worksheet.Cells[row, 8].Value = asset.SerialNumber;
                worksheet.Cells[row, 9].Value = asset.AssignedTo;
                worksheet.Cells[row, 10].Value = asset.PurchaseDate?.ToString("yyyy-MM-dd");
                worksheet.Cells[row, 11].Value = asset.Value;
                worksheet.Cells[row, 12].Value = asset.Vendor;
                worksheet.Cells[row, 13].Value = asset.Description;
                worksheet.Cells[row, 14].Value = asset.Location;
                worksheet.Cells[row, 15].Value = asset.LastMaintenanceDate?.ToString("yyyy-MM-dd");
                worksheet.Cells[row, 16].Value = asset.NextMaintenanceDate?.ToString("yyyy-MM-dd");
                worksheet.Cells[row, 17].Value = asset.CreatedAt.ToString("yyyy-MM-dd HH:mm:ss");
                worksheet.Cells[row, 18].Value = asset.UpdatedAt.ToString("yyyy-MM-dd HH:mm:ss");
            }

            // 自动调整列宽
            worksheet.Cells.AutoFitColumns();

            return await Task.FromResult(package.GetAsByteArray());
        }

        public async Task<byte[]> ExportActivityLogsToExcelAsync(List<ActivityLog> activityLogs)
        {
            ExcelPackage.LicenseContext = LicenseContext.NonCommercial;
            
            using var package = new ExcelPackage();
            var worksheet = package.Workbook.Worksheets.Add("活动日志");

            // 设置表头
            var headers = new[]
            {
                "ID", "活动类型", "描述", "资产ID", "资产名称", "资产编号", 
                "用户ID", "用户名", "旧值", "新值", "创建时间"
            };

            for (int i = 0; i < headers.Length; i++)
            {
                worksheet.Cells[1, i + 1].Value = headers[i];
                worksheet.Cells[1, i + 1].Style.Font.Bold = true;
                worksheet.Cells[1, i + 1].Style.Fill.PatternType = OfficeOpenXml.Style.ExcelFillStyle.Solid;
                worksheet.Cells[1, i + 1].Style.Fill.BackgroundColor.SetColor(System.Drawing.Color.LightGreen);
            }

            // 填充数据
            for (int i = 0; i < activityLogs.Count; i++)
            {
                var log = activityLogs[i];
                var row = i + 2;

                worksheet.Cells[row, 1].Value = log.Id;
                worksheet.Cells[row, 2].Value = log.ActivityType.ToString();
                worksheet.Cells[row, 3].Value = log.Description;
                worksheet.Cells[row, 4].Value = log.AssetId;
                worksheet.Cells[row, 5].Value = log.AssetName;
                worksheet.Cells[row, 6].Value = log.AssetNumber;
                worksheet.Cells[row, 7].Value = log.UserId;
                worksheet.Cells[row, 8].Value = log.UserName;
                worksheet.Cells[row, 9].Value = log.OldValues;
                worksheet.Cells[row, 10].Value = log.NewValues;
                worksheet.Cells[row, 11].Value = log.CreatedAt.ToString("yyyy-MM-dd HH:mm:ss");
            }

            // 自动调整列宽
            worksheet.Cells.AutoFitColumns();

            return await Task.FromResult(package.GetAsByteArray());
        }
    }
} 