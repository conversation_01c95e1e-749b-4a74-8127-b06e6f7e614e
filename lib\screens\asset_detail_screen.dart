import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:go_router/go_router.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import '../providers/asset_provider.dart';
import '../models/asset.dart';
import '../config/routes.dart';
import '../widgets/main_layout.dart';

class AssetDetailScreen extends StatefulWidget {
  final String assetId;
  
  const AssetDetailScreen({
    super.key,
    required this.assetId,
  });

  @override
  State<AssetDetailScreen> createState() => _AssetDetailScreenState();
}

class _AssetDetailScreenState extends State<AssetDetailScreen> {
  Asset? _asset;
  bool _isLoading = true;
  String? _errorMessage;

  @override
  void initState() {
    super.initState();
    _loadAsset();
  }

  Future<void> _loadAsset() async {
    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    try {
      final assetProvider = Provider.of<AssetProvider>(context, listen: false);
      final asset = await assetProvider.getAsset(widget.assetId);
      
      if (mounted) {
        setState(() {
          _asset = asset;
          _isLoading = false;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _errorMessage = e.toString();
          _isLoading = false;
        });
      }
    }
  }

  Future<void> _deleteAsset() async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('确认删除'),
        content: Text('确定要删除资产 "${_asset?.name}" 吗？此操作无法撤销。'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('取消'),
          ),
          ElevatedButton(
            onPressed: () => Navigator.of(context).pop(true),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.red,
              foregroundColor: Colors.white,
            ),
            child: const Text('删除'),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      final assetProvider = Provider.of<AssetProvider>(context, listen: false);
      final success = await assetProvider.deleteAsset(widget.assetId);
      
      if (mounted) {
        if (success) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('资产删除成功')),
          );
          context.go(AppRoutes.assetList);
        } else {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text('删除失败: ${assetProvider.errorMessage}')),
          );
        }
      }
    }
  }

  String _getCategoryDisplayName(AssetCategory category) {
    switch (category) {
      case AssetCategory.laptop:
        return '笔记本电脑';
      case AssetCategory.desktop:
        return '台式电脑';
      case AssetCategory.monitor:
        return '显示器';
      case AssetCategory.printer:
        return '打印机';
      case AssetCategory.phone:
        return '手机';
      case AssetCategory.tablet:
        return '平板电脑';
      case AssetCategory.server:
        return '服务器';
      case AssetCategory.network:
        return '网络设备';
      case AssetCategory.other:
        return '其他';
    }
  }

  String _getStatusDisplayName(AssetStatus status) {
    switch (status) {
      case AssetStatus.available:
        return '未分配';
      case AssetStatus.assigned:
        return '已分配';
      case AssetStatus.maintenance:
        return '维护中';
      case AssetStatus.retired:
        return '已报废';
    }
  }

  Color _getStatusColor(AssetStatus status) {
    switch (status) {
      case AssetStatus.available:
        return Colors.green;
      case AssetStatus.assigned:
        return Colors.blue;
      case AssetStatus.maintenance:
        return Colors.orange;
      case AssetStatus.retired:
        return Colors.red;
    }
  }

  IconData _getCategoryIcon(AssetCategory category) {
    switch (category) {
      case AssetCategory.laptop:
        return FontAwesomeIcons.laptop;
      case AssetCategory.desktop:
        return FontAwesomeIcons.desktop;
      case AssetCategory.monitor:
        return FontAwesomeIcons.tv;
      case AssetCategory.printer:
        return FontAwesomeIcons.print;
      case AssetCategory.phone:
        return FontAwesomeIcons.mobileScreen;
      case AssetCategory.tablet:
        return FontAwesomeIcons.tablet;
      case AssetCategory.server:
        return FontAwesomeIcons.server;
      case AssetCategory.network:
        return FontAwesomeIcons.wifi;
      case AssetCategory.other:
        return FontAwesomeIcons.cube;
    }
  }

  Widget _buildInfoCard(String title, String? value, {IconData? icon}) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Row(
          children: [
            if (icon != null) ...[
              Icon(icon, color: Colors.grey[600]),
              const SizedBox(width: 12),
            ],
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    title,
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color: Colors.grey[600],
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    value ?? '未设置',
                    style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return MainLayout(
      currentRoute: '/assets/${widget.assetId}',
      showBackButton: true,
      title: '资产详情',
      child: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : _errorMessage != null
              ? Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(
                        Icons.error_outline,
                        size: 64,
                        color: Colors.red.shade400,
                      ),
                      const SizedBox(height: 16),
                      Text(
                        '加载失败',
                        style: Theme.of(context).textTheme.headlineSmall,
                      ),
                      const SizedBox(height: 8),
                      Text(
                        _errorMessage!,
                        textAlign: TextAlign.center,
                        style: Theme.of(context).textTheme.bodyMedium,
                      ),
                      const SizedBox(height: 16),
                      ElevatedButton(
                        onPressed: _loadAsset,
                        child: const Text('重试'),
                      ),
                    ],
                  ),
                )
              : _asset == null
                  ? const Center(
                      child: Text('资产不存在'),
                    )
                  : RefreshIndicator(
                      onRefresh: _loadAsset,
                      child: SingleChildScrollView(
                        padding: const EdgeInsets.all(16),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            // 顶部操作栏
                            Row(
                              mainAxisAlignment: MainAxisAlignment.end,
                              children: [
                                IconButton(
                                  icon: const Icon(Icons.edit),
                                  onPressed: () {
                                    context.go('/assets/${widget.assetId}/edit');
                                  },
                                  tooltip: '编辑资产',
                                ),
                                PopupMenuButton<String>(
                                  onSelected: (value) {
                                    switch (value) {
                                      case 'delete':
                                        _deleteAsset();
                                        break;
                                    }
                                  },
                                  itemBuilder: (context) => [
                                    const PopupMenuItem(
                                      value: 'delete',
                                      child: Row(
                                        children: [
                                          Icon(Icons.delete, color: Colors.red),
                                          SizedBox(width: 8),
                                          Text('删除', style: TextStyle(color: Colors.red)),
                                        ],
                                      ),
                                    ),
                                  ],
                                ),
                              ],
                            ),

                            // 资产头部信息
                            Card(
                              child: Padding(
                                padding: const EdgeInsets.all(20),
                                child: Column(
                                  children: [
                                    CircleAvatar(
                                      radius: 40,
                                      backgroundColor: _getStatusColor(_asset!.status),
                                      child: FaIcon(
                                        _getCategoryIcon(_asset!.category),
                                        color: Colors.white,
                                        size: 32,
                                      ),
                                    ),
                                    const SizedBox(height: 16),
                                    Text(
                                      _asset!.name,
                                      style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                                        fontWeight: FontWeight.bold,
                                      ),
                                      textAlign: TextAlign.center,
                                    ),
                                    const SizedBox(height: 8),
                                    Container(
                                      padding: const EdgeInsets.symmetric(
                                        horizontal: 16,
                                        vertical: 8,
                                      ),
                                      decoration: BoxDecoration(
                                        color: _getStatusColor(_asset!.status).withOpacity(0.1),
                                        borderRadius: BorderRadius.circular(20),
                                        border: Border.all(
                                          color: _getStatusColor(_asset!.status),
                                          width: 1,
                                        ),
                                      ),
                                      child: Text(
                                        _getStatusDisplayName(_asset!.status),
                                        style: TextStyle(
                                          color: _getStatusColor(_asset!.status),
                                          fontWeight: FontWeight.bold,
                                        ),
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            ),

                            const SizedBox(height: 16),

                            // 基本信息
                            Text(
                              '基本信息',
                              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                            const SizedBox(height: 12),

                            _buildInfoCard('资产编号', _asset!.assetNumber, icon: Icons.qr_code),
                            _buildInfoCard('分类', _getCategoryDisplayName(_asset!.category), icon: Icons.category),
                            _buildInfoCard('品牌', _asset!.brand, icon: Icons.business),
                            _buildInfoCard('型号', _asset!.model, icon: Icons.info),
                            if (_asset!.serialNumber != null)
                              _buildInfoCard('序列号', _asset!.serialNumber, icon: Icons.tag),

                            const SizedBox(height: 16),

                            // 财务信息
                            Text(
                              '财务信息',
                              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                            const SizedBox(height: 12),

                            if (_asset!.value != null)
                              _buildInfoCard('价值', '¥${_asset!.value!.toStringAsFixed(2)}', icon: Icons.attach_money),
                            if (_asset!.purchaseDate != null)
                              _buildInfoCard('购买日期', _asset!.purchaseDate!.toLocal().toString().split(' ')[0], icon: Icons.calendar_today),
                            if (_asset!.vendor != null)
                              _buildInfoCard('供应商', _asset!.vendor, icon: Icons.store),

                            const SizedBox(height: 16),

                            // 分配信息
                            Text(
                              '分配信息',
                              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                            const SizedBox(height: 12),

                            _buildInfoCard('分配给', _asset!.assignedTo, icon: Icons.person),
                            if (_asset!.location != null)
                              _buildInfoCard('位置', _asset!.location, icon: Icons.location_on),

                            const SizedBox(height: 16),

                            // 其他信息
                            if (_asset!.description != null) ...[
                              Text(
                                '描述',
                                style: Theme.of(context).textTheme.titleLarge?.copyWith(
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                              const SizedBox(height: 12),
                              Card(
                                child: Padding(
                                  padding: const EdgeInsets.all(16),
                                  child: SizedBox(
                                    width: double.infinity,
                                    child: Text(
                                      _asset!.description!,
                                      style: Theme.of(context).textTheme.bodyLarge,
                                    ),
                                  ),
                                ),
                              ),
                            ],

                            const SizedBox(height: 80),
                          ],
                        ),
                      ),
                    ),
    );
  }
}
