@echo off
echo =================================
echo Flutter项目快速修复脚本
echo =================================

echo 1. 清理Flutter项目...
flutter clean

echo 2. 获取所有依赖包...
flutter pub get

echo 3. 生成代码（如果有）...
flutter packages pub run build_runner build --delete-conflicting-outputs 2>nul

echo 4. 检查依赖状态...
flutter pub deps

echo 5. 分析代码问题...
flutter analyze

echo 6. 尝试编译项目...
flutter build apk --debug

echo =================================
echo 修复完成！
echo 如果仍有问题，请检查具体错误信息
echo =================================
pause 