import 'dart:typed_data';
import 'package:excel/excel.dart';
import '../models/asset.dart';
import '../models/activity_log.dart';
import '../models/user.dart';
import 'package:intl/intl.dart';
import 'package:flutter/foundation.dart';

// 条件导入
import 'excel_service_web.dart' if (dart.library.io) 'excel_service_mobile.dart' as platform;

class ExcelService {
  static Future<String> exportAssetsToExcel(List<Asset> assets, {bool saveToDownload = false}) async {
    // 创建Excel文件
    var excel = Excel.createExcel();
    Sheet sheetObject = excel['Assets'];

    // 设置表头
    List<String> headers = [
      'ID',
      '资产名称',
      '类别',
      '序列号',
      '状态',
      '分配给',
      '购买日期',
      '价值',
      '描述',
      '位置',
      '上次维护日期',
      '下次维护日期',
      '创建时间',
      '更新时间',
    ];

    // 添加表头
    for (int i = 0; i < headers.length; i++) {
      var cell = sheetObject.cell(CellIndex.indexByColumnRow(columnIndex: i, rowIndex: 0));
      cell.value = TextCellValue(headers[i]);
      
      // 设置表头样式
      cell.cellStyle = CellStyle(
        bold: true,
        backgroundColorHex: ExcelColor.blue,
        fontColorHex: ExcelColor.white,
      );
    }

    // 添加数据行
    final dateFormat = DateFormat('yyyy-MM-dd');
    final dateTimeFormat = DateFormat('yyyy-MM-dd HH:mm');

    for (int rowIndex = 0; rowIndex < assets.length; rowIndex++) {
      final asset = assets[rowIndex];
      final dataRowIndex = rowIndex + 1;

      List<dynamic> rowData = [
        asset.id ?? '',
        asset.name,
        asset.category.toString().split('.').last,
        asset.serialNumber ?? '',
        asset.status.toString().split('.').last,
        asset.assignedTo ?? '',
        asset.purchaseDate != null 
            ? dateFormat.format(asset.purchaseDate!) 
            : '',
        asset.value ?? 0,
        asset.description ?? '',
        asset.location ?? '',
        asset.lastMaintenanceDate != null 
            ? dateFormat.format(asset.lastMaintenanceDate!) 
            : '',
        asset.nextMaintenanceDate != null 
            ? dateFormat.format(asset.nextMaintenanceDate!) 
            : '',
        asset.createdAt != null 
            ? dateTimeFormat.format(asset.createdAt!) 
            : '',
        asset.updatedAt != null 
            ? dateTimeFormat.format(asset.updatedAt!) 
            : '',
      ];

      for (int colIndex = 0; colIndex < rowData.length; colIndex++) {
        var cell = sheetObject.cell(CellIndex.indexByColumnRow(
          columnIndex: colIndex, 
          rowIndex: dataRowIndex
        ));
        
        final value = rowData[colIndex];
        if (value is String) {
          cell.value = TextCellValue(value);
        } else if (value is num) {
          cell.value = DoubleCellValue(value.toDouble());
        } else {
          cell.value = TextCellValue(value.toString());
        }
      }
    }

    // 自动调整列宽
    for (int i = 0; i < headers.length; i++) {
      sheetObject.setColumnWidth(i, 15.0);
    }

    // 生成文件名
    final timestamp = DateFormat('yyyyMMdd_HHmmss').format(DateTime.now());
    final fileName = 'assets_export_$timestamp.xlsx';
    
    final bytes = excel.encode();
    if (bytes != null) {
      return await platform.saveFile(bytes, fileName, saveToDownload: saveToDownload);
    }
    
    throw Exception('生成Excel文件失败');
  }

  static Future<String> exportActivityLogsToExcel(List<ActivityLog> activityLogs, {bool saveToDownload = false}) async {
    // 创建Excel文件
    var excel = Excel.createExcel();
    Sheet sheetObject = excel['ActivityLogs'];

    // 设置表头
    List<String> headers = [
      'ID',
      '活动类型',
      '描述',
      '资产ID',
      '资产名称',
      '资产编号',
      '用户ID',
      '用户名',
      '旧值',
      '新值',
      '创建时间',
    ];

    // 添加表头
    for (int i = 0; i < headers.length; i++) {
      var cell = sheetObject.cell(CellIndex.indexByColumnRow(columnIndex: i, rowIndex: 0));
      cell.value = TextCellValue(headers[i]);
      
      // 设置表头样式
      cell.cellStyle = CellStyle(
        bold: true,
        backgroundColorHex: ExcelColor.orange,
        fontColorHex: ExcelColor.white,
      );
    }

    // 添加数据行
    final dateTimeFormat = DateFormat('yyyy-MM-dd HH:mm:ss');

    for (int rowIndex = 0; rowIndex < activityLogs.length; rowIndex++) {
      final log = activityLogs[rowIndex];
      final dataRowIndex = rowIndex + 1;

      List<dynamic> rowData = [
        log.id,
        log.activityTypeDisplayName,
        log.description,
        log.assetId ?? '',
        log.assetName ?? '',
        log.assetNumber ?? '',
        log.userId,
        log.userName,
        log.oldValues ?? '',
        log.newValues ?? '',
        dateTimeFormat.format(log.createdAt),
      ];

      for (int colIndex = 0; colIndex < rowData.length; colIndex++) {
        var cell = sheetObject.cell(CellIndex.indexByColumnRow(
          columnIndex: colIndex, 
          rowIndex: dataRowIndex
        ));
        
        final value = rowData[colIndex];
        if (value is String) {
          cell.value = TextCellValue(value);
        } else if (value is num) {
          cell.value = DoubleCellValue(value.toDouble());
        } else {
          cell.value = TextCellValue(value.toString());
        }
      }
    }

    // 自动调整列宽
    for (int i = 0; i < headers.length; i++) {
      sheetObject.setColumnWidth(i, 15.0);
    }

    // 生成文件名
    final timestamp = DateFormat('yyyyMMdd_HHmmss').format(DateTime.now());
    final fileName = 'activity_logs_export_$timestamp.xlsx';
    
    final bytes = excel.encode();
    if (bytes != null) {
      return await platform.saveFile(bytes, fileName, saveToDownload: saveToDownload);
    }
    
    throw Exception('生成Excel文件失败');
  }

  static Future<String> exportUsersToExcel(List<User> users, {bool saveToDownload = false}) async {
    var excel = Excel.createExcel();
    Sheet sheetObject = excel['Users'];

    // 设置表头
    List<String> headers = [
      'ID',
      '用户名',
      '邮箱',
      '角色',
      '全名',
      '部门',
      '状态',
      '分配资产数',
      '创建时间',
      '更新时间',
    ];

    // 添加表头
    for (int i = 0; i < headers.length; i++) {
      var cell = sheetObject.cell(CellIndex.indexByColumnRow(columnIndex: i, rowIndex: 0));
      cell.value = TextCellValue(headers[i]);
      
      cell.cellStyle = CellStyle(
        bold: true,
        backgroundColorHex: ExcelColor.green,
        fontColorHex: ExcelColor.white,
      );
    }

    // 添加数据行
    final dateTimeFormat = DateFormat('yyyy-MM-dd HH:mm');

    for (int rowIndex = 0; rowIndex < users.length; rowIndex++) {
      final user = users[rowIndex];
      final dataRowIndex = rowIndex + 1;

      List<dynamic> rowData = [
        user.id,
        user.username,
        user.email,
        user.role,
        user.fullName ?? '',
        user.department ?? '',
        user.isActive ? '激活' : '已禁用',
        user.assignedAssetsCount,
        dateTimeFormat.format(user.createdAt),
        dateTimeFormat.format(user.updatedAt),
      ];

      for (int colIndex = 0; colIndex < rowData.length; colIndex++) {
        var cell = sheetObject.cell(CellIndex.indexByColumnRow(
          columnIndex: colIndex, 
          rowIndex: dataRowIndex
        ));
        
        final value = rowData[colIndex];
        if (value is String) {
          cell.value = TextCellValue(value);
        } else if (value is num) {
          cell.value = DoubleCellValue(value.toDouble());
        } else {
          cell.value = TextCellValue(value.toString());
        }
      }
    }

    // 自动调整列宽
    for (int i = 0; i < headers.length; i++) {
      sheetObject.setColumnWidth(i, 15.0);
    }

    // 生成文件名
    final timestamp = DateFormat('yyyyMMdd_HHmmss').format(DateTime.now());
    final fileName = 'users_export_$timestamp.xlsx';
    
    final bytes = excel.encode();
    if (bytes != null) {
      return await platform.saveFile(bytes, fileName, saveToDownload: saveToDownload);
    }
    
    throw Exception('生成Excel文件失败');
  }
}
