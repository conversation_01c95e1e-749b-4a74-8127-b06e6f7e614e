class DashboardStats {
  final int totalAssets;
  final int assignedAssets;
  final int availableAssets;
  final int maintenanceAssets;
  final int retiredAssets;
  final List<CategoryStats> categoryStats;
  final List<MonthlyStats> monthlyStats;

  DashboardStats({
    required this.totalAssets,
    required this.assignedAssets,
    required this.availableAssets,
    required this.maintenanceAssets,
    required this.retiredAssets,
    required this.categoryStats,
    required this.monthlyStats,
  });

  factory DashboardStats.fromJson(Map<String, dynamic> json) {
    return DashboardStats(
      totalAssets: json['totalAssets'] is int ? json['totalAssets'] : int.tryParse(json['totalAssets'].toString()) ?? 0,
      assignedAssets: json['assignedAssets'] is int ? json['assignedAssets'] : int.tryParse(json['assignedAssets'].toString()) ?? 0,
      availableAssets: json['availableAssets'] is int ? json['availableAssets'] : int.tryParse(json['availableAssets'].toString()) ?? 0,
      maintenanceAssets: json['maintenanceAssets'] is int ? json['maintenanceAssets'] : int.tryParse(json['maintenanceAssets'].toString()) ?? 0,
      retiredAssets: json['retiredAssets'] is int ? json['retiredAssets'] : int.tryParse(json['retiredAssets'].toString()) ?? 0,
      categoryStats: (json['categoryStats'] as List? ?? [])
          .map((item) => CategoryStats.fromJson(item))
          .toList(),
      monthlyStats: (json['monthlyStats'] as List? ?? [])
          .map((item) => MonthlyStats.fromJson(item))
          .toList(),
    );
  }

  int get unassignedAssets => availableAssets;
  int get needingMaintenance => maintenanceAssets;
}

class CategoryStats {
  final String category;
  final int count;

  CategoryStats({
    required this.category,
    required this.count,
  });

  factory CategoryStats.fromJson(Map<String, dynamic> json) {
    return CategoryStats(
      category: json['category']?.toString() ?? '',
      count: json['count'] is int ? json['count'] : int.tryParse(json['count'].toString()) ?? 0,
    );
  }
}

class MonthlyStats {
  final String month;
  final int purchased;
  final int retired;

  MonthlyStats({
    required this.month,
    required this.purchased,
    required this.retired,
  });

  factory MonthlyStats.fromJson(Map<String, dynamic> json) {
    return MonthlyStats(
      month: json['month']?.toString() ?? '',
      purchased: json['purchased'] is int ? json['purchased'] : int.tryParse(json['purchased'].toString()) ?? 0,
      retired: json['retired'] is int ? json['retired'] : int.tryParse(json['retired'].toString()) ?? 0,
    );
  }
}
