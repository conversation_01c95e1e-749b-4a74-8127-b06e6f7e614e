using Microsoft.EntityFrameworkCore;
using ITAssetAPI.Models;
using BCrypt.Net;

namespace ITAssetAPI.Data
{
    public static class SeedData
    {
        public static async Task Initialize(IServiceProvider serviceProvider)
        {
            using var context = new ApplicationDbContext(
                serviceProvider.GetRequiredService<DbContextOptions<ApplicationDbContext>>());

            // 检查是否已有数据
            if (context.Users.Any())
            {
                return; // 数据库已初始化
            }

            // 创建用户
            var users = new[]
            {
                new User
                {
                    Username = "admin",
                    PasswordHash = BCrypt.Net.BCrypt.HashPassword("admin123"),
                    Email = "<EMAIL>",
                    Role = "Admin",
                    FullName = "系统管理员",
                    Department = "IT部门"
                },
                new User
                {
                    Username = "user",
                    PasswordHash = BCrypt.Net.BCrypt.HashPassword("user123"),
                    Email = "<EMAIL>",
                    Role = "Normal User",
                    FullName = "普通用户",
                    Department = "财务部门"
                }
            };

            context.Users.AddRange(users);
            await context.SaveChangesAsync();

            // 创建资产
            var assets = new[]
            {
                new Asset
                {
                    Name = "MacBook Pro 16\"",
                    AssetNumber = "AST-001",
                    Category = AssetCategory.Laptop,
                    Status = AssetStatus.Assigned,
                    Brand = "Apple",
                    Model = "MacBook Pro 16\" 2023",
                    SerialNumber = "MBP2023001",
                    AssignedTo = "张三",
                    PurchaseDate = new DateTime(2023, 1, 15, 0, 0, 0, DateTimeKind.Utc),
                    Value = 18999.0m,
                    Vendor = "苹果授权经销商",
                    Description = "高性能开发笔记本",
                    Location = "研发部",
                    LastMaintenanceDate = new DateTime(2023, 6, 15, 0, 0, 0, DateTimeKind.Utc),
                    NextMaintenanceDate = new DateTime(2024, 6, 15, 0, 0, 0, DateTimeKind.Utc)
                },
                new Asset
                {
                    Name = "Dell OptiPlex 7090",
                    AssetNumber = "AST-002",
                    Category = AssetCategory.Desktop,
                    Status = AssetStatus.Available,
                    Brand = "Dell",
                    Model = "OptiPlex 7090",
                    SerialNumber = "DELL2023002",
                    PurchaseDate = new DateTime(2023, 2, 20, 0, 0, 0, DateTimeKind.Utc),
                    Value = 8999.0m,
                    Vendor = "戴尔中国",
                    Description = "办公台式机",
                    Location = "仓库",
                    NextMaintenanceDate = new DateTime(2024, 2, 20, 0, 0, 0, DateTimeKind.Utc)
                },
                new Asset
                {
                    Name = "HP LaserJet Pro",
                    AssetNumber = "AST-003",
                    Category = AssetCategory.Printer,
                    Status = AssetStatus.Maintenance,
                    Brand = "HP",
                    Model = "LaserJet Pro M404dn",
                    SerialNumber = "HP2023003",
                    PurchaseDate = new DateTime(2023, 3, 10, 0, 0, 0, DateTimeKind.Utc),
                    Value = 2999.0m,
                    Vendor = "惠普中国",
                    Description = "激光打印机",
                    Location = "办公区",
                    LastMaintenanceDate = new DateTime(2023, 12, 1, 0, 0, 0, DateTimeKind.Utc),
                    NextMaintenanceDate = new DateTime(2024, 3, 1, 0, 0, 0, DateTimeKind.Utc)
                },
                new Asset
                {
                    Name = "iPhone 15 Pro",
                    AssetNumber = "AST-004",
                    Category = AssetCategory.Phone,
                    Status = AssetStatus.Assigned,
                    Brand = "Apple",
                    Model = "iPhone 15 Pro",
                    SerialNumber = "IP2023004",
                    AssignedTo = "李四",
                    PurchaseDate = new DateTime(2023, 9, 20, 0, 0, 0, DateTimeKind.Utc),
                    Value = 9999.0m,
                    Vendor = "苹果授权经销商",
                    Description = "公司手机",
                    Location = "销售部"
                },
                new Asset
                {
                    Name = "ThinkPad X1 Carbon",
                    AssetNumber = "AST-005",
                    Category = AssetCategory.Laptop,
                    Status = AssetStatus.Retired,
                    Brand = "Lenovo",
                    Model = "ThinkPad X1 Carbon Gen 8",
                    SerialNumber = "TP2023005",
                    PurchaseDate = new DateTime(2020, 5, 15, 0, 0, 0, DateTimeKind.Utc),
                    Value = 12999.0m,
                    Vendor = "联想中国",
                    Description = "已退役笔记本",
                    Location = "仓库",
                    LastMaintenanceDate = new DateTime(2023, 5, 15, 0, 0, 0, DateTimeKind.Utc)
                },
                new Asset
                {
                    Name = "LG UltraWide Monitor",
                    AssetNumber = "AST-006",
                    Category = AssetCategory.Monitor,
                    Status = AssetStatus.Available,
                    Brand = "LG",
                    Model = "34WN750-B",
                    SerialNumber = "LG2023006",
                    PurchaseDate = new DateTime(2023, 4, 12, 0, 0, 0, DateTimeKind.Utc),
                    Value = 3299.0m,
                    Vendor = "LG电子",
                    Description = "34寸超宽显示器",
                    Location = "仓库",
                    NextMaintenanceDate = new DateTime(2024, 4, 12, 0, 0, 0, DateTimeKind.Utc)
                },
                new Asset
                {
                    Name = "iPad Pro 12.9\"",
                    AssetNumber = "AST-007",
                    Category = AssetCategory.Tablet,
                    Status = AssetStatus.Assigned,
                    Brand = "Apple",
                    Model = "iPad Pro 12.9\" (6th generation)",
                    SerialNumber = "IPD2023007",
                    AssignedTo = "王五",
                    PurchaseDate = new DateTime(2023, 7, 8, 0, 0, 0, DateTimeKind.Utc),
                    Value = 8799.0m,
                    Vendor = "苹果授权经销商",
                    Description = "设计用平板电脑",
                    Location = "设计部"
                }
            };

            context.Assets.AddRange(assets);
            await context.SaveChangesAsync();
        }
    }
} 