@echo off
echo 设置IT资产管理数据库...
echo.

cd Backend\ITAssetAPI

echo 检查.NET环境...
dotnet --version
if %errorlevel% neq 0 (
    echo 错误: 未找到.NET SDK，请先安装.NET 6.0或更高版本
    pause
    exit /b 1
)

echo.
echo 检查Entity Framework工具...
dotnet tool list -g | findstr "dotnet-ef" >nul
if %errorlevel% neq 0 (
    echo 安装Entity Framework工具...
    dotnet tool install --global dotnet-ef
)

echo.
echo 还原NuGet包...
dotnet restore

echo.
echo 构建项目...
dotnet build

echo.
echo 创建数据库迁移...
dotnet ef migrations add InitialCreate --force

echo.
echo 更新数据库...
dotnet ef database update

echo.
echo 数据库设置完成！
echo.
echo 现在可以运行 start_backend.bat 启动服务器

pause 