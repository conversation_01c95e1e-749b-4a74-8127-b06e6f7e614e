using System.ComponentModel.DataAnnotations;

namespace ITAssetAPI.Models
{
    public enum AssetStatus
    {
        Available,
        Assigned,
        Maintenance,
        Retired
    }

    public enum AssetCategory
    {
        Laptop,
        Desktop,
        Monitor,
        Printer,
        Phone,
        Tablet,
        Server,
        Network,
        Other
    }

    public class Asset
    {
        public int Id { get; set; }
        
        [Required]
        [MaxLength(100)]
        public string Name { get; set; } = string.Empty;
        
        [Required]
        [MaxLength(50)]
        public string AssetNumber { get; set; } = string.Empty;
        
        public AssetCategory Category { get; set; }
        
        public AssetStatus Status { get; set; } = AssetStatus.Available;
        
        [MaxLength(50)]
        public string? Brand { get; set; }
        
        [MaxLength(100)]
        public string? Model { get; set; }
        
        [MaxLength(100)]
        public string? SerialNumber { get; set; }
        
        [MaxLength(100)]
        public string? AssignedTo { get; set; }
        
        public DateTime? PurchaseDate { get; set; }
        
        public decimal? Value { get; set; }
        
        [MaxLength(100)]
        public string? Vendor { get; set; }
        
        public string? Description { get; set; }
        
        [MaxLength(100)]
        public string? Location { get; set; }
        
        public DateTime? LastMaintenanceDate { get; set; }
        
        public DateTime? NextMaintenanceDate { get; set; }
        
        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
        
        public DateTime UpdatedAt { get; set; } = DateTime.UtcNow;
        
        // Foreign key
        public int? AssignedUserId { get; set; }
        
        // Navigation property
        public User? AssignedUser { get; set; }
    }
} 