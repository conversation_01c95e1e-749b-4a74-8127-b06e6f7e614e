import 'package:flutter/material.dart';
import 'package:intl/intl.dart';

class CompactSearchBar extends StatefulWidget {
  final TextEditingController searchController;
  final String? selectedActivityType;
  final DateTime? startDate;
  final DateTime? endDate;
  final List<String> activityTypes;
  final VoidCallback onSearch;
  final VoidCallback onFilterChanged;
  final VoidCallback onClearFilters;
  final VoidCallback? onSearchChanged;
  final Function(String?) onActivityTypeChanged;
  final Function(DateTime?) onStartDateChanged;
  final Function(DateTime?) onEndDateChanged;

  const CompactSearchBar({
    super.key,
    required this.searchController,
    required this.selectedActivityType,
    required this.startDate,
    required this.endDate,
    required this.activityTypes,
    required this.onSearch,
    required this.onFilterChanged,
    required this.onClearFilters,
    this.onSearchChanged,
    required this.onActivityTypeChanged,
    required this.onStartDateChanged,
    required this.onEndDateChanged,
  });

  @override
  State<CompactSearchBar> createState() => _CompactSearchBarState();
}

class _CompactSearchBarState extends State<CompactSearchBar> {
  bool _isFilterExpanded = false;

  String _getActivityTypeDisplayName(String type) {
    switch (type) {
      case 'Create':
        return '创建';
      case 'Update':
        return '更新';
      case 'Delete':
        return '删除';
      case 'Assign':
        return '分配';
      case 'Unassign':
        return '取消分配';
      case 'Maintenance':
        return '维护';
      default:
        return type;
    }
  }

  Future<void> _selectStartDate(BuildContext context) async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: widget.startDate ?? DateTime.now(),
      firstDate: DateTime(2020),
      lastDate: DateTime.now(),
    );
    if (picked != null) {
      widget.onStartDateChanged(picked);
    }
  }

  Future<void> _selectEndDate(BuildContext context) async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: widget.endDate ?? DateTime.now(),
      firstDate: widget.startDate ?? DateTime(2020),
      lastDate: DateTime.now(),
    );
    if (picked != null) {
      widget.onEndDateChanged(picked);
    }
  }

  int _getActiveFilterCount() {
    int count = 0;
    if (widget.selectedActivityType != null) count++;
    if (widget.startDate != null) count++;
    if (widget.endDate != null) count++;
    return count;
  }

  @override
  Widget build(BuildContext context) {
    final activeFilterCount = _getActiveFilterCount();
    
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: Column(
        children: [
          // 紧凑的搜索栏
          Row(
            children: [
              // 搜索框
              Expanded(
                child: Container(
                  height: 40,
                  decoration: BoxDecoration(
                    color: Colors.grey[100],
                    borderRadius: BorderRadius.circular(20),
                    border: Border.all(color: Colors.grey[300]!),
                  ),
                  child: TextField(
                    controller: widget.searchController,
                    decoration: InputDecoration(
                      hintText: '搜索描述、用户名、资产名称...',
                      hintStyle: TextStyle(
                        fontSize: 14,
                        color: Colors.grey[600],
                      ),
                      prefixIcon: Icon(
                        Icons.search,
                        size: 20,
                        color: Colors.grey[600],
                      ),
                      suffixIcon: widget.searchController.text.isNotEmpty
                          ? IconButton(
                              icon: Icon(
                                Icons.clear,
                                size: 18,
                                color: Colors.grey[600],
                              ),
                              onPressed: () {
                                widget.searchController.clear();
                                widget.onSearch();
                              },
                            )
                          : null,
                      border: InputBorder.none,
                      contentPadding: const EdgeInsets.symmetric(
                        horizontal: 16,
                        vertical: 10,
                      ),
                    ),
                    style: const TextStyle(fontSize: 14),
                    onChanged: (_) => widget.onSearchChanged?.call(),
                    onSubmitted: (_) => widget.onSearch(),
                  ),
                ),
              ),
              const SizedBox(width: 8),
              // 筛选按钮
              Container(
                height: 40,
                decoration: BoxDecoration(
                  color: _isFilterExpanded ? Theme.of(context).primaryColor : Colors.grey[100],
                  borderRadius: BorderRadius.circular(20),
                  border: Border.all(
                    color: _isFilterExpanded 
                        ? Theme.of(context).primaryColor 
                        : Colors.grey[300]!,
                  ),
                ),
                child: Material(
                  color: Colors.transparent,
                  child: InkWell(
                    borderRadius: BorderRadius.circular(20),
                    onTap: () {
                      setState(() {
                        _isFilterExpanded = !_isFilterExpanded;
                      });
                    },
                    child: Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 12),
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Icon(
                            Icons.tune,
                            size: 18,
                            color: _isFilterExpanded 
                                ? Colors.white 
                                : Colors.grey[700],
                          ),
                          if (activeFilterCount > 0) ...[
                            const SizedBox(width: 4),
                            Container(
                              padding: const EdgeInsets.all(2),
                              decoration: BoxDecoration(
                                color: _isFilterExpanded 
                                    ? Colors.white 
                                    : Theme.of(context).primaryColor,
                                shape: BoxShape.circle,
                              ),
                              child: Text(
                                '$activeFilterCount',
                                style: TextStyle(
                                  fontSize: 10,
                                  fontWeight: FontWeight.bold,
                                  color: _isFilterExpanded 
                                      ? Theme.of(context).primaryColor 
                                      : Colors.white,
                                ),
                              ),
                            ),
                          ],
                        ],
                      ),
                    ),
                  ),
                ),
              ),
            ],
          ),
          
          // 可展开的筛选选项
          AnimatedContainer(
            duration: const Duration(milliseconds: 300),
            height: _isFilterExpanded ? null : 0,
            child: _isFilterExpanded
                ? Container(
                    margin: const EdgeInsets.only(top: 12),
                    padding: const EdgeInsets.all(12),
                    decoration: BoxDecoration(
                      color: Colors.grey[50],
                      borderRadius: BorderRadius.circular(12),
                      border: Border.all(color: Colors.grey[200]!),
                    ),
                    child: Column(
                      children: [
                        // 活动类型选择
                        Row(
                          children: [
                            Icon(
                              Icons.category_outlined,
                              size: 16,
                              color: Colors.grey[600],
                            ),
                            const SizedBox(width: 8),
                            Expanded(
                              child: DropdownButtonFormField<String>(
                                value: widget.selectedActivityType,
                                decoration: InputDecoration(
                                  labelText: '活动类型',
                                  labelStyle: const TextStyle(fontSize: 12),
                                  border: OutlineInputBorder(
                                    borderRadius: BorderRadius.circular(8),
                                  ),
                                  contentPadding: const EdgeInsets.symmetric(
                                    horizontal: 12,
                                    vertical: 8,
                                  ),
                                  isDense: true,
                                  filled: true,
                                  fillColor: Colors.white,
                                ),
                                style: const TextStyle(
                                  fontSize: 14,
                                  color: Colors.black87,
                                ),
                                dropdownColor: Colors.white,
                                items: [
                                  const DropdownMenuItem<String>(
                                    value: null,
                                    child: Text(
                                      '全部类型',
                                      style: TextStyle(color: Colors.black87),
                                    ),
                                  ),
                                  ...widget.activityTypes.map((type) => 
                                    DropdownMenuItem<String>(
                                      value: type,
                                      child: Text(
                                        _getActivityTypeDisplayName(type),
                                        style: const TextStyle(color: Colors.black87),
                                      ),
                                    ),
                                  ),
                                ],
                                onChanged: widget.onActivityTypeChanged,
                              ),
                            ),
                          ],
                        ),
                        const SizedBox(height: 12),
                        
                        // 日期范围选择
                        Row(
                          children: [
                            Icon(
                              Icons.date_range_outlined,
                              size: 16,
                              color: Colors.grey[600],
                            ),
                            const SizedBox(width: 8),
                            Expanded(
                              child: InkWell(
                                onTap: () => _selectStartDate(context),
                                child: Container(
                                  padding: const EdgeInsets.symmetric(
                                    horizontal: 12,
                                    vertical: 12,
                                  ),
                                  decoration: BoxDecoration(
                                    color: Colors.white,
                                    border: Border.all(color: Colors.grey[400]!),
                                    borderRadius: BorderRadius.circular(8),
                                  ),
                                  child: Row(
                                    children: [
                                      Expanded(
                                        child: Text(
                                          widget.startDate != null
                                              ? DateFormat('MM/dd').format(widget.startDate!)
                                              : '开始日期',
                                          style: TextStyle(
                                            fontSize: 12,
                                            color: widget.startDate != null 
                                                ? Colors.black87 
                                                : Colors.grey[600],
                                          ),
                                        ),
                                      ),
                                      Icon(
                                        Icons.calendar_today,
                                        size: 14,
                                        color: Colors.grey[600],
                                      ),
                                    ],
                                  ),
                                ),
                              ),
                            ),
                            const SizedBox(width: 8),
                            Expanded(
                              child: InkWell(
                                onTap: () => _selectEndDate(context),
                                child: Container(
                                  padding: const EdgeInsets.symmetric(
                                    horizontal: 12,
                                    vertical: 12,
                                  ),
                                  decoration: BoxDecoration(
                                    color: Colors.white,
                                    border: Border.all(color: Colors.grey[400]!),
                                    borderRadius: BorderRadius.circular(8),
                                  ),
                                  child: Row(
                                    children: [
                                      Expanded(
                                        child: Text(
                                          widget.endDate != null
                                              ? DateFormat('MM/dd').format(widget.endDate!)
                                              : '结束日期',
                                          style: TextStyle(
                                            fontSize: 12,
                                            color: widget.endDate != null 
                                                ? Colors.black87 
                                                : Colors.grey[600],
                                          ),
                                        ),
                                      ),
                                      Icon(
                                        Icons.calendar_today,
                                        size: 14,
                                        color: Colors.grey[600],
                                      ),
                                    ],
                                  ),
                                ),
                              ),
                            ),
                          ],
                        ),
                        
                        // 操作按钮
                        if (activeFilterCount > 0) ...[
                          const SizedBox(height: 12),
                          Row(
                            mainAxisAlignment: MainAxisAlignment.end,
                            children: [
                              TextButton.icon(
                                onPressed: widget.onClearFilters,
                                icon: const Icon(Icons.clear_all, size: 16),
                                label: const Text('清除筛选'),
                                style: TextButton.styleFrom(
                                  foregroundColor: Colors.grey[700],
                                  textStyle: const TextStyle(fontSize: 12),
                                ),
                              ),
                            ],
                          ),
                        ],
                      ],
                    ),
                  )
                : const SizedBox.shrink(),
          ),
        ],
      ),
    );
  }
} 